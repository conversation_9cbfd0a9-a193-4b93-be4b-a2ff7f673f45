# 招标文件智能审查与提取系统

基于MaxKB智能体的招标文件智能审查与提取系统，支持Word文档上传、自动化审查、异常检测和结构化数据提取功能。

## 项目架构

- **前端**: React + TypeScript + Ant Design
- **后端**: Python FastAPI + uvicorn
- **文档处理**: python-docx + python-mammoth
- **智能体集成**: MaxKB HTTP API

## 功能特性

- ✅ Word文档上传和解析
- ✅ 文档内容HTML转换和显示
- ✅ 智能异常检测和审查
- ✅ 107个标准化数据点提取
- ✅ 精确文档内容定位和高亮
- ✅ 三栏布局用户界面
- ✅ AI助手交互功能

## 快速开始

### 环境要求

- Node.js 18+
- Python 3.11+
- Docker (可选)

### 本地开发

#### 后端启动

```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动

```bash
cd frontend
npm install
npm run dev
```

### Docker 部署

```bash
docker-compose up -d
```

## 访问地址

- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## MaxKB智能体

- 审查智能体: http://*************:8080/ui/chat/25d81420107e8030
- 提取智能体: http://*************:8080/ui/chat/2d9ee65d4a2b0c4d

## 项目结构

```
├── backend/           # Python FastAPI后端
│   ├── app/
│   │   ├── api/       # API路由
│   │   ├── services/  # 业务逻辑
│   │   ├── models/    # 数据模型
│   │   └── utils/     # 工具函数
│   └── temp/          # 临时文件
├── frontend/          # React前端
│   └── src/
│       ├── components/ # React组件
│       ├── services/   # API服务
│       └── types/      # TypeScript类型
└── docs/              # 项目文档
```

## 开发指南

### API接口

- `POST /api/upload` - 文件上传
- `POST /api/process/{session_id}` - 文档处理
- `GET /api/status/{session_id}` - 处理状态
- `GET /api/results/{session_id}` - 获取结果
- `DELETE /api/cleanup/{session_id}` - 清理文件

### 验收标准

- 支持Word文档上传（50MB限制）
- 文档解析和HTML转换正确
- MaxKB智能体API调用成功
- 文档定位精度<5字符
- 处理时间<30秒
- 完善的错误处理

## 许可证

MIT License
