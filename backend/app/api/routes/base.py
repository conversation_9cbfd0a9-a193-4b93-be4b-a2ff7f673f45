"""
基础路由
提供系统基础功能接口
"""

from fastapi import APIRouter, HTTPException
from datetime import datetime
import os
import uuid

from app.models.schemas import BaseResponse

router = APIRouter()

@router.get("/info", summary="系统信息")
async def get_system_info():
    """
    获取系统基本信息
    """
    return {
        "name": "招标文件智能审查与提取系统",
        "version": "1.0.0",
        "description": "基于MaxKB智能体的招标文件智能审查与提取系统",
        "features": [
            "Word文档上传和解析",
            "文档内容HTML转换和显示", 
            "智能异常检测和审查",
            "107个标准化数据点提取",
            "精确文档内容定位和高亮",
            "三栏布局用户界面",
            "AI助手交互功能"
        ],
        "maxkb_agents": {
            "review_agent": "25d81420107e8030",
            "extract_agent": "2d9ee65d4a2b0c4d"
        },
        "api_endpoints": {
            "upload": "/api/upload",
            "process": "/api/process/{session_id}",
            "status": "/api/status/{session_id}",
            "results": "/api/results/{session_id}",
            "cleanup": "/api/cleanup/{session_id}"
        },
        "timestamp": datetime.now().isoformat()
    }

@router.get("/version", summary="版本信息")
async def get_version():
    """
    获取系统版本信息
    """
    return {
        "version": "1.0.0",
        "build_date": "2025-08-03",
        "api_version": "v1",
        "python_version": "3.11+",
        "framework": "FastAPI 0.104.1"
    }

@router.post("/session", summary="创建会话")
async def create_session():
    """
    创建新的处理会话
    
    返回唯一的session_id用于后续操作
    """
    session_id = str(uuid.uuid4())
    
    # 创建会话临时目录
    session_dir = f"temp/{session_id}"
    try:
        os.makedirs(session_dir, exist_ok=True)
        
        return BaseResponse(
            success=True,
            message="会话创建成功",
            timestamp=datetime.now().isoformat()
        ).dict() | {
            "session_id": session_id,
            "session_dir": session_dir,
            "expires_in": 86400  # 24小时过期
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建会话失败: {str(e)}"
        )

@router.get("/sessions/{session_id}/exists", summary="检查会话是否存在")
async def check_session_exists(session_id: str):
    """
    检查指定会话是否存在
    """
    session_dir = f"temp/{session_id}"
    exists = os.path.exists(session_dir)
    
    return {
        "session_id": session_id,
        "exists": exists,
        "session_dir": session_dir if exists else None,
        "timestamp": datetime.now().isoformat()
    }

@router.get("/config", summary="系统配置")
async def get_config():
    """
    获取系统配置信息
    """
    return {
        "max_file_size": 52428800,  # 50MB
        "allowed_extensions": [".doc", ".docx"],
        "temp_dir": "temp",
        "session_timeout": 86400,  # 24小时
        "supported_languages": ["zh-CN", "en-US"],
        "maxkb_config": {
            "base_url": "http://119.3.106.119:8080",
            "review_agent_id": "25d81420107e8030",
            "extract_agent_id": "2d9ee65d4a2b0c4d",
            "timeout": 30
        },
        "extraction_points": 107,
        "timestamp": datetime.now().isoformat()
    }

@router.get("/stats", summary="系统统计")
async def get_stats():
    """
    获取系统使用统计信息
    """
    try:
        # 统计临时目录中的会话数量
        temp_dir = "temp"
        session_count = 0
        total_files = 0
        
        if os.path.exists(temp_dir):
            sessions = [d for d in os.listdir(temp_dir) 
                       if os.path.isdir(os.path.join(temp_dir, d))]
            session_count = len(sessions)
            
            # 统计文件数量
            for session in sessions:
                session_path = os.path.join(temp_dir, session)
                if os.path.exists(session_path):
                    files = [f for f in os.listdir(session_path) 
                            if os.path.isfile(os.path.join(session_path, f))]
                    total_files += len(files)
        
        return {
            "active_sessions": session_count,
            "total_files": total_files,
            "temp_directory": temp_dir,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取统计信息失败: {str(e)}"
        )
