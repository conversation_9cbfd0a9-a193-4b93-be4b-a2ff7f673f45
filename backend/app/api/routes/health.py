"""
健康检查路由
提供系统健康状态检查接口
"""

from fastapi import APIRouter, Depends
from datetime import datetime
import time
import psutil
import sys
import os

from app.models.schemas import HealthResponse

router = APIRouter()

# 应用启动时间
start_time = time.time()

def get_system_info():
    """获取系统信息"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        return {
            "cpu_usage": f"{cpu_percent}%",
            "memory_usage": f"{memory_percent}%",
            "disk_usage": f"{disk_percent}%",
            "python_version": sys.version.split()[0]
        }
    except Exception as e:
        return {"error": str(e)}

@router.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """
    健康检查接口
    
    返回系统运行状态、版本信息和依赖服务状态
    """
    current_time = time.time()
    uptime = current_time - start_time
    
    # 检查依赖服务状态
    dependencies = {
        "python": sys.version.split()[0],
        "fastapi": "0.104.1",
        "uvicorn": "0.24.0"
    }
    
    # 添加系统信息
    system_info = get_system_info()
    dependencies.update(system_info)
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        uptime=round(uptime, 2),
        dependencies=dependencies
    )

@router.get("/health/detailed", summary="详细健康检查")
async def detailed_health_check():
    """
    详细健康检查接口
    
    返回更详细的系统状态信息
    """
    current_time = time.time()
    uptime = current_time - start_time
    
    try:
        # 检查临时目录
        temp_dir = "temp"
        temp_dir_exists = os.path.exists(temp_dir)
        temp_dir_writable = os.access(temp_dir, os.W_OK) if temp_dir_exists else False
        
        # 检查磁盘空间
        disk_usage = psutil.disk_usage('.')
        free_space_gb = disk_usage.free / (1024**3)
        
        # 进程信息
        process = psutil.Process()
        process_info = {
            "pid": process.pid,
            "memory_info": process.memory_info()._asdict(),
            "cpu_percent": process.cpu_percent(),
            "create_time": datetime.fromtimestamp(process.create_time()).isoformat()
        }
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "uptime_seconds": round(uptime, 2),
            "system": {
                "platform": sys.platform,
                "python_version": sys.version,
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "disk_free_gb": round(free_space_gb, 2)
            },
            "process": process_info,
            "directories": {
                "temp_exists": temp_dir_exists,
                "temp_writable": temp_dir_writable
            },
            "dependencies": {
                "fastapi": "0.104.1",
                "uvicorn": "0.24.0",
                "python-docx": "1.1.0",
                "python-mammoth": "1.6.0",
                "httpx": "0.25.2"
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "uptime_seconds": round(uptime, 2)
        }
