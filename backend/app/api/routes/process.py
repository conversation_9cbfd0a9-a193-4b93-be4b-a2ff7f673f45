"""
文档处理API路由
处理文档解析、分析和结果获取
"""

import os
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

from app.models.schemas import (
    BaseResponse, 
    ProcessStatusResponse, 
    ProcessStatus,
    ProcessResultResponse
)
from app.config import settings
from app.services.document_parser import DocumentParser
from app.services.maxkb_client import MaxKBClient
from app.services.cache_manager import CacheManager
from app.utils.file_utils import get_session_files

router = APIRouter()

# 全局服务实例
document_parser = DocumentParser()
maxkb_client = MaxKBClient()
cache_manager = CacheManager()

# 存储处理状态的字典（实际应用中应使用数据库或缓存）
processing_status = {}

@router.post("/parse/{session_id}", response_model=BaseResponse, summary="解析文档")
async def parse_document(session_id: str, background_tasks: BackgroundTasks):
    """
    解析指定会话的Word文档
    
    Args:
        session_id: 会话ID
        background_tasks: 后台任务
        
    Returns:
        BaseResponse: 解析启动响应
    """
    try:
        # 检查会话是否存在
        session_dir = os.path.join(settings.temp_dir, session_id)
        if not os.path.exists(session_dir):
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )
        
        # 获取会话中的文件
        files = get_session_files(session_id)
        if not files:
            raise HTTPException(
                status_code=400,
                detail="会话中没有找到文件"
            )
        
        # 获取第一个文件进行处理
        target_file = files[0]
        filename = target_file["filename"]
        
        # 检查是否已在处理中
        if session_id in processing_status:
            current_status = processing_status[session_id]
            if current_status["status"] in [ProcessStatus.PARSING, ProcessStatus.REVIEWING, ProcessStatus.EXTRACTING]:
                return BaseResponse(
                    success=True,
                    message="文档正在处理中，请稍候",
                    timestamp=datetime.now().isoformat()
                )
        
        # 设置初始处理状态
        processing_status[session_id] = {
            "status": ProcessStatus.PARSING,
            "progress": 0,
            "current_step": "开始解析文档",
            "filename": filename,
            "start_time": datetime.now().isoformat(),
            "estimated_time": 30
        }
        
        # 启动后台解析任务
        background_tasks.add_task(
            process_document_background, 
            session_id, 
            filename
        )
        
        return BaseResponse(
            success=True,
            message="文档解析已启动",
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"启动文档解析失败: {str(e)}"
        )

@router.get("/status/{session_id}", response_model=ProcessStatusResponse, summary="查询处理状态")
async def get_process_status(session_id: str):
    """
    查询文档处理状态
    
    Args:
        session_id: 会话ID
        
    Returns:
        ProcessStatusResponse: 处理状态响应
    """
    try:
        # 检查会话是否存在
        if session_id not in processing_status:
            # 检查会话目录是否存在
            session_dir = os.path.join(settings.temp_dir, session_id)
            if not os.path.exists(session_dir):
                raise HTTPException(
                    status_code=404,
                    detail="会话不存在"
                )
            
            # 返回初始状态
            return ProcessStatusResponse(
                success=True,
                message="会话存在但未开始处理",
                session_id=session_id,
                status=ProcessStatus.UPLOADED,
                progress=0,
                current_step="等待开始处理",
                timestamp=datetime.now().isoformat()
            )
        
        status_info = processing_status[session_id]
        
        return ProcessStatusResponse(
            success=True,
            message="状态查询成功",
            session_id=session_id,
            status=status_info["status"],
            progress=status_info["progress"],
            current_step=status_info["current_step"],
            estimated_time=status_info.get("estimated_time"),
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询处理状态失败: {str(e)}"
        )

@router.get("/results/{session_id}", summary="获取处理结果")
async def get_process_results(session_id: str):
    """
    获取文档处理结果
    
    Args:
        session_id: 会话ID
        
    Returns:
        Dict: 处理结果
    """
    try:
        # 检查处理状态
        if session_id not in processing_status:
            raise HTTPException(
                status_code=404,
                detail="会话不存在或未开始处理"
            )
        
        status_info = processing_status[session_id]
        
        if status_info["status"] != ProcessStatus.COMPLETED:
            return {
                "success": False,
                "message": "文档处理尚未完成",
                "session_id": session_id,
                "current_status": status_info["status"],
                "progress": status_info["progress"]
            }
        
        # 获取处理结果
        results = status_info.get("results")

        if results is None:
            raise HTTPException(
                status_code=500,
                detail="处理结果不可用"
            )

        # 确保results是字典类型
        if not isinstance(results, dict):
            raise HTTPException(
                status_code=500,
                detail="处理结果格式错误"
            )
        
        return {
            "success": True,
            "message": "获取处理结果成功",
            "session_id": session_id,
            "document_html": results.get("html_content", ""),
            "text_content": results.get("text_content", ""),
            "document_structure": results.get("document_structure", {}),
            "text_mapping": results.get("text_mapping", {}),
            "statistics": results.get("statistics", {}),
            "processing_time": results.get("processing_time", 0),
            "anomalies": results.get("review_result", {}).get("anomalies", []),
            "extractions": results.get("extract_result", {}).get("extractions", []),
            "total_anomalies": results.get("review_result", {}).get("total_anomalies", 0),
            "total_extractions": results.get("extract_result", {}).get("total_extractions", 0),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取处理结果失败: {str(e)}"
        )

async def process_document_background(session_id: str, filename: str):
    """
    后台文档处理任务
    
    Args:
        session_id: 会话ID
        filename: 文件名
    """
    try:
        start_time = datetime.now()
        
        # 更新状态：解析中
        processing_status[session_id].update({
            "status": ProcessStatus.PARSING,
            "progress": 10,
            "current_step": "正在解析Word文档"
        })
        
        # 执行文档解析
        parse_result = await document_parser.parse_document(session_id, filename)
        
        if not parse_result["success"]:
            # 解析失败
            processing_status[session_id].update({
                "status": ProcessStatus.ERROR,
                "progress": 0,
                "current_step": f"文档解析失败: {parse_result.get('error', '未知错误')}",
                "error": parse_result.get("error")
            })
            return
        
        # 更新状态：开始智能体处理
        processing_status[session_id].update({
            "status": ProcessStatus.REVIEWING,
            "progress": 50,
            "current_step": "正在调用智能体进行审查和提取"
        })

        # 调用MaxKB智能体
        try:
            # 读取文件内容
            file_path = os.path.join(settings.temp_dir, session_id, filename)
            file_content = None

            try:
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                logger.info(f"成功读取文件: {file_path}, 大小: {len(file_content)} bytes")
            except FileNotFoundError:
                logger.error(f"文件不存在: {file_path}")
                raise Exception(f"文件不存在: {file_path}")
            except Exception as e:
                logger.error(f"读取文件失败: {str(e)}")
                raise Exception(f"读取文件失败: {str(e)}")

            # 调用MaxKB客户端，传递文件内容
            agent_result = await maxkb_client.process_document_with_agents(
                session_id, parse_result.get("text_content", ""), file_content, filename
            )

            # 合并结果
            final_result = {**parse_result}
            if agent_result and isinstance(agent_result, dict):
                final_result.update(agent_result)
            else:
                # 如果智能体调用失败，提供默认值
                final_result.update({
                    "review_result": {"anomalies": [], "total_anomalies": 0, "summary": "智能体调用失败"},
                    "extract_result": {"extractions": [], "total_extractions": 0, "summary": "智能体调用失败"},
                    "errors": ["智能体调用失败"]
                })
        except Exception as e:
            logger.error(f"智能体调用异常: {str(e)}")
            # 智能体调用失败时，仍然返回文档解析结果
            final_result = {**parse_result}
            final_result.update({
                "review_result": {"anomalies": [], "total_anomalies": 0, "summary": f"智能体调用失败: {str(e)}"},
                "extract_result": {"extractions": [], "total_extractions": 0, "summary": f"智能体调用失败: {str(e)}"},
                "errors": [f"智能体调用失败: {str(e)}"]
            })

        # 更新状态：处理完成
        processing_status[session_id].update({
            "status": ProcessStatus.COMPLETED,
            "progress": 100,
            "current_step": "文档处理完成",
            "results": final_result,
            "processing_time": (datetime.now() - start_time).total_seconds()
        })
        
    except Exception as e:
        # 处理异常
        processing_status[session_id].update({
            "status": ProcessStatus.ERROR,
            "progress": 0,
            "current_step": f"处理过程中发生错误: {str(e)}",
            "error": str(e)
        })

@router.delete("/cleanup/{session_id}", summary="清理处理数据")
async def cleanup_process_data(session_id: str):
    """
    清理指定会话的处理数据
    
    Args:
        session_id: 会话ID
        
    Returns:
        Dict: 清理结果
    """
    try:
        # 清理处理状态
        if session_id in processing_status:
            del processing_status[session_id]
        
        return {
            "success": True,
            "message": "处理数据清理成功",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"清理处理数据失败: {str(e)}"
        )
