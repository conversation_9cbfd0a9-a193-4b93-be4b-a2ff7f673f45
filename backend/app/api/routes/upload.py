"""
文件上传API路由
处理Word文档上传、验证和存储
"""

import os
import uuid
import shutil
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from app.models.schemas import FileUploadResponse, ErrorResponse
from app.config import settings
from app.utils.file_utils import (
    validate_file_type, 
    validate_file_size, 
    create_session_directory,
    save_uploaded_file
)

router = APIRouter()

@router.post("/upload", response_model=FileUploadResponse, summary="文件上传")
async def upload_file(file: UploadFile = File(...)):
    """
    上传Word文档文件
    
    支持的文件格式：.doc, .docx
    文件大小限制：50MB
    
    Returns:
        FileUploadResponse: 包含session_id和文件信息的响应
    """
    try:
        # 1. 验证文件类型
        if not validate_file_type(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型。仅支持: {', '.join(settings.allowed_extensions)}"
            )
        
        # 2. 验证文件大小
        file_content = await file.read()
        if not validate_file_size(len(file_content)):
            max_size_mb = settings.max_file_size / (1024 * 1024)
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制。最大允许: {max_size_mb:.1f}MB"
            )
        
        # 3. 生成唯一session_id
        session_id = str(uuid.uuid4())
        
        # 4. 创建会话目录
        session_dir = create_session_directory(session_id)
        
        # 5. 保存文件
        file_path = await save_uploaded_file(file_content, file.filename, session_dir)
        
        # 6. 返回成功响应
        return FileUploadResponse(
            success=True,
            message="文件上传成功",
            session_id=session_id,
            filename=file.filename,
            file_size=len(file_content),
            upload_time=datetime.now().isoformat(),
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )

@router.get("/upload/config", summary="获取上传配置")
async def get_upload_config():
    """
    获取文件上传配置信息
    """
    return {
        "max_file_size": settings.max_file_size,
        "max_file_size_mb": round(settings.max_file_size / (1024 * 1024), 1),
        "allowed_extensions": settings.allowed_extensions,
        "temp_directory": settings.temp_dir,
        "supported_formats": [
            {
                "extension": ".doc",
                "description": "Microsoft Word 97-2003 文档"
            },
            {
                "extension": ".docx", 
                "description": "Microsoft Word 2007+ 文档"
            }
        ]
    }

@router.delete("/upload/{session_id}", summary="删除上传文件")
async def delete_uploaded_file(session_id: str):
    """
    删除指定会话的上传文件
    """
    try:
        session_dir = os.path.join(settings.temp_dir, session_id)
        
        if not os.path.exists(session_dir):
            raise HTTPException(
                status_code=404,
                detail="会话不存在或文件已被删除"
            )
        
        # 删除整个会话目录
        shutil.rmtree(session_dir)
        
        return {
            "success": True,
            "message": "文件删除成功",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除文件失败: {str(e)}"
        )
