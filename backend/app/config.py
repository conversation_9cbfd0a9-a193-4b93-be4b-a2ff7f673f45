"""
应用配置文件
管理应用的配置参数
"""

import os
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    """应用设置"""

    # 基础配置
    app_name: str = "招标文件智能审查与提取系统"
    app_version: str = "1.0.0"
    debug: bool = True

    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8000

    # CORS配置
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001"
    ]

    # 文件上传配置
    max_file_size: int = 52428800  # 50MB
    allowed_extensions: List[str] = [".doc", ".docx"]
    temp_dir: str = "temp"

    # MaxKB智能体配置
    # 检测智能体配置
    maxkb_review_base_url: str = "http://*************:8080/api/application/4df82ff0-615a-11f0-badc-02420a370103"
    maxkb_review_api_key: str = "application-47f5f4a8127ec92961d1b4da8291c8ef"

    # 提取智能体配置
    maxkb_extract_base_url: str = "http://*************:8080/api/application/36c0f176-612a-11f0-b769-02420a370103"
    maxkb_extract_api_key: str = "application-451b7a2742cb67333fb6781dd8902a08"

    # 通用配置
    maxkb_timeout: int = 300  # 增加超时时间到5分钟
    maxkb_test_mode: bool = False  # 关闭测试模式

    # 会话配置
    session_timeout: int = 86400  # 24小时

    # 日志配置
    log_level: str = "INFO"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建全局设置实例
settings = Settings()

# 确保临时目录存在 - 使用绝对路径
import sys
script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
temp_path = os.path.join(script_dir, settings.temp_dir)
os.makedirs(temp_path, exist_ok=True)
print(f"临时目录创建在: {temp_path}")
