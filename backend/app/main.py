"""
FastAPI应用入口文件
招标文件智能审查与提取系统后端
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn
import logging
from datetime import datetime

try:
    # 尝试相对导入（当作为模块运行时）
    from .api.routes import health, base, upload, process
    from .models.schemas import ErrorResponse
    from .config import settings
except ImportError:
    # 回退到绝对导入（当直接运行时）
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from app.api.routes import health, base
    from app.models.schemas import ErrorResponse
    from app.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    description="基于MaxKB智能体的招标文件智能审查与提取系统API",
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=True,
            message=exc.detail,
            timestamp=datetime.now().isoformat()
        ).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"请求验证错误: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            error=True,
            message="请求参数验证失败",
            details=exc.errors(),
            timestamp=datetime.now().isoformat()
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error=True,
            message="服务器内部错误",
            timestamp=datetime.now().isoformat()
        ).dict()
    )

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    logger.info("🚀 招标文件智能审查与提取系统启动中...")
    logger.info("📚 API文档地址: http://localhost:8000/docs")

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    logger.info("🛑 招标文件智能审查与提取系统正在关闭...")

# 注册路由
app.include_router(health.router, prefix="/api", tags=["健康检查"])
app.include_router(base.router, prefix="/api", tags=["基础接口"])
app.include_router(upload.router, prefix="/api", tags=["文件上传"])
app.include_router(process.router, prefix="/api", tags=["文档处理"])

# 根路径
@app.get("/", summary="根路径", description="系统根路径，返回基本信息")
async def root():
    """根路径接口"""
    return {
        "message": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "docs": "/docs"
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
