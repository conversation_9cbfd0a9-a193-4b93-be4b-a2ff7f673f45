"""
MaxKB API数据模型
定义与MaxKB智能体交互的数据结构
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict
from datetime import datetime
from enum import Enum

# MaxKB请求和响应基础模型
class MaxKBRequest(BaseModel):
    """MaxKB智能体请求模型"""
    message: str = Field(..., description="发送给智能体的消息")
    session_id: str = Field(..., description="会话ID")
    agent_type: str = Field(..., description="智能体类型: review 或 extract")

class MaxKBResponse(BaseModel):
    """MaxKB智能体响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# 异常检测相关模型
class AnomalyLevel(str, Enum):
    """异常严重级别"""
    CRITICAL = "critical"  # 严重
    WARNING = "warning"    # 警告
    INFO = "info"         # 提示

class AnomalyType(str, Enum):
    """异常类型"""
    MISSING_INFO = "missing_info"           # 信息缺失
    INCONSISTENT_DATA = "inconsistent_data" # 数据不一致
    FORMAT_ERROR = "format_error"           # 格式错误
    LOGIC_ERROR = "logic_error"             # 逻辑错误
    COMPLIANCE_ISSUE = "compliance_issue"   # 合规问题
    OTHER = "other"                         # 其他

class AnomalyItem(BaseModel):
    """异常项模型"""
    id: str = Field(..., description="异常唯一标识")
    type: AnomalyType = Field(..., description="异常类型")
    level: AnomalyLevel = Field(..., description="严重级别")
    title: str = Field(..., description="异常标题")
    description: str = Field(..., description="异常详细描述")
    location: str = Field(..., description="文档位置描述")
    page_number: Optional[int] = Field(None, description="页码")
    paragraph_number: Optional[int] = Field(None, description="段落号")
    suggestion: Optional[str] = Field(None, description="修改建议")
    original_text: Optional[str] = Field(None, description="相关原文")
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")

class ReviewResult(BaseModel):
    """审查结果模型"""
    anomalies: List[AnomalyItem] = Field(default=[], description="异常列表")
    summary: str = Field(default="", description="审查总结")
    total_anomalies: int = Field(default=0, description="异常总数")
    review_time: str = Field(default_factory=lambda: datetime.now().isoformat())
    
    def model_post_init(self, __context):
        """模型初始化后处理"""
        if self.total_anomalies == 0:
            self.total_anomalies = len(self.anomalies)

# 数据提取相关模型
class ExtractionCategory(str, Enum):
    """提取数据分类"""
    BASIC_INFO = "basic_info"               # 基本信息
    BIDDER_REQUIREMENTS = "bidder_requirements"  # 投标人要求
    TECHNICAL_SPECS = "technical_specs"     # 技术规格
    COMMERCIAL_TERMS = "commercial_terms"   # 商务条款
    EVALUATION_CRITERIA = "evaluation_criteria"  # 评标标准
    CONTRACT_TERMS = "contract_terms"       # 合同条款
    OTHER = "other"                         # 其他

class ExtractionItem(BaseModel):
    """提取项模型"""
    id: str = Field(..., description="提取点唯一标识")
    name: str = Field(..., description="数据点名称")
    category: ExtractionCategory = Field(..., description="分类")
    content: Optional[str] = Field(None, description="提取的内容")
    location: str = Field(..., description="文档位置描述")
    page_number: Optional[int] = Field(None, description="页码")
    paragraph_number: Optional[int] = Field(None, description="段落号")
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")
    original_text: Optional[str] = Field(None, description="相关原文")
    is_required: bool = Field(default=False, description="是否为必填项")
    data_type: str = Field(default="text", description="数据类型")

class ExtractionResult(BaseModel):
    """提取结果模型"""
    extractions: List[ExtractionItem] = Field(default=[], description="提取列表")
    summary: str = Field(default="", description="提取总结")
    total_extractions: int = Field(default=0, description="提取总数")
    completion_rate: float = Field(default=0.0, description="完成率")
    extraction_time: str = Field(default_factory=lambda: datetime.now().isoformat())
    
    def model_post_init(self, __context):
        """模型初始化后处理"""
        if self.total_extractions == 0:
            self.total_extractions = len(self.extractions)
        
        # 计算完成率（有内容的提取项 / 总提取项）
        if self.total_extractions > 0:
            completed = sum(1 for item in self.extractions if item.content)
            self.completion_rate = completed / self.total_extractions

# 智能体处理结果综合模型
class AgentProcessResult(BaseModel):
    """智能体处理结果模型"""
    session_id: str = Field(..., description="会话ID")
    success: bool = Field(..., description="处理是否成功")
    message: str = Field(default="", description="处理消息")
    review_result: Optional[ReviewResult] = Field(None, description="审查结果")
    extract_result: Optional[ExtractionResult] = Field(None, description="提取结果")
    errors: List[str] = Field(default=[], description="错误列表")
    processing_time: float = Field(default=0.0, description="处理耗时(秒)")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# 智能体配置模型
class AgentConfig(BaseModel):
    """智能体配置模型"""
    agent_id: str = Field(..., description="智能体ID")
    agent_type: str = Field(..., description="智能体类型")
    base_url: str = Field(..., description="基础URL")
    timeout: int = Field(default=30, description="超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟(秒)")

# 预定义的107个标准提取点
STANDARD_EXTRACTION_POINTS = [
    # 基本信息 (1-20)
    {"id": "001", "name": "项目名称", "category": "basic_info", "is_required": True},
    {"id": "002", "name": "招标编号", "category": "basic_info", "is_required": True},
    {"id": "003", "name": "招标人", "category": "basic_info", "is_required": True},
    {"id": "004", "name": "招标代理机构", "category": "basic_info", "is_required": False},
    {"id": "005", "name": "项目预算", "category": "basic_info", "is_required": True},
    {"id": "006", "name": "投标截止时间", "category": "basic_info", "is_required": True},
    {"id": "007", "name": "开标时间", "category": "basic_info", "is_required": True},
    {"id": "008", "name": "开标地点", "category": "basic_info", "is_required": True},
    {"id": "009", "name": "联系人", "category": "basic_info", "is_required": True},
    {"id": "010", "name": "联系电话", "category": "basic_info", "is_required": True},
    
    # 投标人要求 (21-40)
    {"id": "021", "name": "注册资本要求", "category": "bidder_requirements", "is_required": True},
    {"id": "022", "name": "资质等级要求", "category": "bidder_requirements", "is_required": True},
    {"id": "023", "name": "业绩要求", "category": "bidder_requirements", "is_required": True},
    {"id": "024", "name": "财务状况要求", "category": "bidder_requirements", "is_required": True},
    {"id": "025", "name": "人员配备要求", "category": "bidder_requirements", "is_required": True},
    
    # 技术规格 (41-70)
    {"id": "041", "name": "技术标准", "category": "technical_specs", "is_required": True},
    {"id": "042", "name": "质量要求", "category": "technical_specs", "is_required": True},
    {"id": "043", "name": "性能指标", "category": "technical_specs", "is_required": True},
    
    # 商务条款 (71-90)
    {"id": "071", "name": "付款方式", "category": "commercial_terms", "is_required": True},
    {"id": "072", "name": "交付期限", "category": "commercial_terms", "is_required": True},
    {"id": "073", "name": "质保期", "category": "commercial_terms", "is_required": True},
    
    # 评标标准 (91-100)
    {"id": "091", "name": "评标方法", "category": "evaluation_criteria", "is_required": True},
    {"id": "092", "name": "技术分权重", "category": "evaluation_criteria", "is_required": True},
    {"id": "093", "name": "商务分权重", "category": "evaluation_criteria", "is_required": True},
    
    # 合同条款 (101-107)
    {"id": "101", "name": "合同期限", "category": "contract_terms", "is_required": True},
    {"id": "102", "name": "违约责任", "category": "contract_terms", "is_required": True},
    {"id": "103", "name": "争议解决", "category": "contract_terms", "is_required": True},
    {"id": "104", "name": "知识产权", "category": "contract_terms", "is_required": False},
    {"id": "105", "name": "保密条款", "category": "contract_terms", "is_required": False},
    {"id": "106", "name": "不可抗力", "category": "contract_terms", "is_required": False},
    {"id": "107", "name": "其他条款", "category": "contract_terms", "is_required": False},
]
