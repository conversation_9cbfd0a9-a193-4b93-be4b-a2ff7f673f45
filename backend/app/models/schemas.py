"""
Pydantic数据模型定义
定义API请求和响应的数据结构
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict
from datetime import datetime
from enum import Enum

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: bool = True
    message: str
    details: Optional[Any] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# 文件上传相关模型
class FileUploadResponse(BaseResponse):
    """文件上传响应模型"""
    session_id: str = Field(..., description="会话ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    upload_time: str = Field(..., description="上传时间")

# 处理状态枚举
class ProcessStatus(str, Enum):
    """处理状态枚举"""
    UPLOADED = "uploaded"
    PARSING = "parsing"
    REVIEWING = "reviewing"
    EXTRACTING = "extracting"
    COMPLETED = "completed"
    ERROR = "error"

# 处理状态响应模型
class ProcessStatusResponse(BaseResponse):
    """处理状态响应模型"""
    session_id: str = Field(..., description="会话ID")
    status: ProcessStatus = Field(..., description="处理状态")
    progress: int = Field(default=0, ge=0, le=100, description="进度百分比")
    current_step: str = Field(default="", description="当前步骤")
    estimated_time: Optional[int] = Field(None, description="预计剩余时间(秒)")

# 异常检测相关模型
class AnomalyLevel(str, Enum):
    """异常严重级别"""
    CRITICAL = "critical"  # 严重
    WARNING = "warning"    # 警告
    INFO = "info"         # 提示

class AnomalyItem(BaseModel):
    """异常项模型"""
    id: str = Field(..., description="异常ID")
    type: str = Field(..., description="异常类型")
    level: AnomalyLevel = Field(..., description="严重级别")
    title: str = Field(..., description="异常标题")
    description: str = Field(..., description="异常描述")
    location: str = Field(..., description="文档位置")
    page_number: Optional[int] = Field(None, description="页码")
    paragraph_number: Optional[int] = Field(None, description="段落号")
    suggestion: Optional[str] = Field(None, description="修改建议")
    original_text: Optional[str] = Field(None, description="原文内容")

# 数据提取相关模型
class ExtractionItem(BaseModel):
    """提取项模型"""
    id: str = Field(..., description="提取项ID")
    name: str = Field(..., description="提取点名称")
    category: str = Field(..., description="分类")
    content: Optional[str] = Field(None, description="提取内容")
    location: str = Field(..., description="文档位置")
    page_number: Optional[int] = Field(None, description="页码")
    paragraph_number: Optional[int] = Field(None, description="段落号")
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")
    original_text: Optional[str] = Field(None, description="原文内容")

# 处理结果响应模型
class ProcessResultResponse(BaseResponse):
    """处理结果响应模型"""
    session_id: str = Field(..., description="会话ID")
    document_html: str = Field(..., description="文档HTML内容")
    anomalies: List[AnomalyItem] = Field(default=[], description="异常列表")
    extractions: List[ExtractionItem] = Field(default=[], description="提取列表")
    total_anomalies: int = Field(default=0, description="异常总数")
    total_extractions: int = Field(default=0, description="提取总数")
    processing_time: float = Field(..., description="处理耗时(秒)")

# 健康检查响应模型
class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = "healthy"
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    version: str = "1.0.0"
    uptime: Optional[float] = None
    dependencies: Dict[str, str] = Field(default_factory=dict)

# MaxKB智能体相关模型
class MaxKBRequest(BaseModel):
    """MaxKB智能体请求模型"""
    message: str = Field(..., description="发送给智能体的消息")
    session_id: str = Field(..., description="会话ID")
    type: str = Field(..., description="请求类型: review 或 extract")

class MaxKBResponse(BaseModel):
    """MaxKB智能体响应模型"""
    success: bool = Field(..., description="请求是否成功")
    data: Any = Field(..., description="响应数据")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

# 文档解析相关模型
class DocumentInfo(BaseModel):
    """文档信息模型"""
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    page_count: Optional[int] = Field(None, description="页数")
    word_count: Optional[int] = Field(None, description="字数")
    upload_time: str = Field(..., description="上传时间")
    parse_time: Optional[str] = Field(None, description="解析时间")

# 清理响应模型
class CleanupResponse(BaseResponse):
    """清理响应模型"""
    session_id: str = Field(..., description="会话ID")
    files_deleted: int = Field(default=0, description="删除的文件数量")
    space_freed: int = Field(default=0, description="释放的空间(字节)")
