"""
缓存管理服务
管理智能体调用结果的缓存
"""

import json
import os
import time
from typing import Dict, Optional, Any
from datetime import datetime, timedelta

from app.config import settings

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.cache_dir = os.path.join(settings.temp_dir, "cache")
        self.ensure_cache_dir()
        self.default_ttl = 3600  # 默认缓存1小时
    
    def ensure_cache_dir(self):
        """确保缓存目录存在"""
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def get_cache_key(self, session_id: str, agent_type: str) -> str:
        """
        生成缓存键
        
        Args:
            session_id: 会话ID
            agent_type: 智能体类型
            
        Returns:
            str: 缓存键
        """
        return f"{session_id}_{agent_type}"
    
    def get_cache_file_path(self, cache_key: str) -> str:
        """
        获取缓存文件路径
        
        Args:
            cache_key: 缓存键
            
        Returns:
            str: 缓存文件路径
        """
        return os.path.join(self.cache_dir, f"{cache_key}.json")
    
    def set_cache(self, session_id: str, agent_type: str, data: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存
        
        Args:
            session_id: 会话ID
            agent_type: 智能体类型
            data: 缓存数据
            ttl: 生存时间(秒)
            
        Returns:
            bool: 是否成功
        """
        try:
            cache_key = self.get_cache_key(session_id, agent_type)
            cache_file = self.get_cache_file_path(cache_key)
            
            cache_data = {
                "data": data,
                "timestamp": time.time(),
                "ttl": ttl or self.default_ttl,
                "session_id": session_id,
                "agent_type": agent_type
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"设置缓存失败: {str(e)}")
            return False
    
    def get_cache(self, session_id: str, agent_type: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            session_id: 会话ID
            agent_type: 智能体类型
            
        Returns:
            Optional[Any]: 缓存数据，如果不存在或过期则返回None
        """
        try:
            cache_key = self.get_cache_key(session_id, agent_type)
            cache_file = self.get_cache_file_path(cache_key)
            
            if not os.path.exists(cache_file):
                return None
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查是否过期
            current_time = time.time()
            cache_time = cache_data.get("timestamp", 0)
            ttl = cache_data.get("ttl", self.default_ttl)
            
            if current_time - cache_time > ttl:
                # 缓存过期，删除文件
                self.delete_cache(session_id, agent_type)
                return None
            
            return cache_data.get("data")
            
        except Exception as e:
            print(f"获取缓存失败: {str(e)}")
            return None
    
    def delete_cache(self, session_id: str, agent_type: str) -> bool:
        """
        删除缓存
        
        Args:
            session_id: 会话ID
            agent_type: 智能体类型
            
        Returns:
            bool: 是否成功
        """
        try:
            cache_key = self.get_cache_key(session_id, agent_type)
            cache_file = self.get_cache_file_path(cache_key)
            
            if os.path.exists(cache_file):
                os.remove(cache_file)
            
            return True
            
        except Exception as e:
            print(f"删除缓存失败: {str(e)}")
            return False
    
    def clear_session_cache(self, session_id: str) -> int:
        """
        清理指定会话的所有缓存
        
        Args:
            session_id: 会话ID
            
        Returns:
            int: 清理的缓存文件数量
        """
        try:
            cleared_count = 0
            
            for filename in os.listdir(self.cache_dir):
                if filename.startswith(f"{session_id}_") and filename.endswith(".json"):
                    file_path = os.path.join(self.cache_dir, filename)
                    try:
                        os.remove(file_path)
                        cleared_count += 1
                    except Exception:
                        pass
            
            return cleared_count
            
        except Exception as e:
            print(f"清理会话缓存失败: {str(e)}")
            return 0
    
    def clear_expired_cache(self) -> int:
        """
        清理所有过期缓存
        
        Returns:
            int: 清理的缓存文件数量
        """
        try:
            cleared_count = 0
            current_time = time.time()
            
            for filename in os.listdir(self.cache_dir):
                if not filename.endswith(".json"):
                    continue
                
                file_path = os.path.join(self.cache_dir, filename)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    cache_time = cache_data.get("timestamp", 0)
                    ttl = cache_data.get("ttl", self.default_ttl)
                    
                    if current_time - cache_time > ttl:
                        os.remove(file_path)
                        cleared_count += 1
                        
                except Exception:
                    # 如果文件损坏，也删除它
                    try:
                        os.remove(file_path)
                        cleared_count += 1
                    except Exception:
                        pass
            
            return cleared_count
            
        except Exception as e:
            print(f"清理过期缓存失败: {str(e)}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict: 缓存统计信息
        """
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "expired_files": 0,
                "sessions": set(),
                "agent_types": set()
            }
            
            current_time = time.time()
            
            for filename in os.listdir(self.cache_dir):
                if not filename.endswith(".json"):
                    continue
                
                file_path = os.path.join(self.cache_dir, filename)
                stats["total_files"] += 1
                
                try:
                    # 文件大小
                    stats["total_size"] += os.path.getsize(file_path)
                    
                    # 读取缓存数据
                    with open(file_path, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    # 检查是否过期
                    cache_time = cache_data.get("timestamp", 0)
                    ttl = cache_data.get("ttl", self.default_ttl)
                    
                    if current_time - cache_time > ttl:
                        stats["expired_files"] += 1
                    
                    # 统计会话和智能体类型
                    session_id = cache_data.get("session_id")
                    agent_type = cache_data.get("agent_type")
                    
                    if session_id:
                        stats["sessions"].add(session_id)
                    if agent_type:
                        stats["agent_types"].add(agent_type)
                        
                except Exception:
                    pass
            
            # 转换set为list
            stats["sessions"] = list(stats["sessions"])
            stats["agent_types"] = list(stats["agent_types"])
            
            return stats
            
        except Exception as e:
            return {
                "error": f"获取缓存统计失败: {str(e)}",
                "total_files": 0,
                "total_size": 0
            }
