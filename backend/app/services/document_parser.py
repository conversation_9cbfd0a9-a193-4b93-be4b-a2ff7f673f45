"""
Word文档解析服务
使用python-docx和mammoth处理Word文档
"""

import os
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path

import docx
import mammoth
from docx.document import Document
from docx.text.paragraph import Paragraph

from app.config import settings
from app.utils.text_locator import TextLocator

class DocumentParser:
    """Word文档解析器"""
    
    def __init__(self):
        self.text_locator = TextLocator()
    
    async def parse_document(self, session_id: str, filename: str) -> Dict:
        """
        解析Word文档
        
        Args:
            session_id: 会话ID
            filename: 文件名
            
        Returns:
            Dict: 解析结果
        """
        try:
            # 构建文件路径 - 确保使用绝对路径
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))  # 获取项目根目录
            file_path = os.path.join(base_dir, settings.temp_dir, session_id, filename)

            # 标准化路径
            file_path = os.path.normpath(file_path)

            print(f"DEBUG: 尝试访问文件路径: {file_path}")
            print(f"DEBUG: 文件是否存在: {os.path.exists(file_path)}")

            if os.path.exists(file_path):
                # 检查文件大小和基本信息
                file_size = os.path.getsize(file_path)
                print(f"DEBUG: 文件大小: {file_size} bytes")

                # 检查文件是否可读
                try:
                    with open(file_path, 'rb') as f:
                        first_bytes = f.read(8)
                        print(f"DEBUG: 文件前8字节: {first_bytes}")
                except Exception as e:
                    print(f"DEBUG: 文件读取错误: {e}")

            if not os.path.exists(file_path):
                # 尝试其他可能的路径
                alternative_paths = [
                    os.path.join(settings.temp_dir, session_id, filename),
                    os.path.join("backend", settings.temp_dir, session_id, filename),
                    os.path.join(os.getcwd(), settings.temp_dir, session_id, filename)
                ]

                for alt_path in alternative_paths:
                    alt_path = os.path.normpath(alt_path)
                    print(f"DEBUG: 尝试备用路径: {alt_path}")
                    if os.path.exists(alt_path):
                        file_path = alt_path
                        print(f"DEBUG: 找到文件在: {file_path}")
                        break
                else:
                    raise FileNotFoundError(f"文件不存在: {file_path}，也尝试了备用路径")
            
            # 提取纯文本内容
            text_content = self._extract_text_content(file_path)
            
            # 转换为HTML格式
            html_content = await self._convert_to_html(file_path)
            
            # 提取文档结构信息
            document_structure = self._extract_document_structure(file_path)
            
            # 建立文本位置映射
            text_mapping = self.text_locator.create_text_mapping(
                text_content, html_content, document_structure
            )
            
            # 获取文档统计信息
            doc_stats = self._get_document_statistics(file_path)
            
            return {
                "success": True,
                "session_id": session_id,
                "filename": filename,
                "text_content": text_content,
                "html_content": html_content,
                "document_structure": document_structure,
                "text_mapping": text_mapping,
                "statistics": doc_stats,
                "message": "文档解析成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "session_id": session_id,
                "filename": filename,
                "error": str(e),
                "message": "文档解析失败"
            }
    
    def _extract_text_content(self, file_path: str) -> str:
        """
        使用python-docx提取纯文本内容

        Args:
            file_path: 文件路径

        Returns:
            str: 纯文本内容
        """
        try:
            print(f"DEBUG: 开始解析文档: {file_path}")

            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            print(f"DEBUG: 文件扩展名: {file_ext}")

            # 检查是否是Word文档格式
            if file_ext not in ['.doc', '.docx']:
                raise Exception(f"不支持的文件格式: {file_ext}")

            # 对于.doc文件，可能需要特殊处理
            if file_ext == '.doc':
                print("DEBUG: 检测到.doc格式文件，尝试特殊处理")
                # 尝试使用不同的方法处理.doc文件
                return self._extract_text_from_doc_file(file_path)

            # 处理.docx文件
            print("DEBUG: 使用python-docx处理.docx文件")
            doc = docx.Document(file_path)
            
            # 提取所有段落文本
            paragraphs = []
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:  # 只保留非空段落
                    paragraphs.append(text)
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                    if row_text:
                        paragraphs.append(" | ".join(row_text))
            
            return "\n\n".join(paragraphs)
            
        except Exception as e:
            print(f"DEBUG: 文档解析异常: {str(e)}")
            raise Exception(f"提取文本内容失败: {str(e)}")

    def _extract_text_from_doc_file(self, file_path: str) -> str:
        """
        处理.doc格式文件的特殊方法

        Args:
            file_path: 文件路径

        Returns:
            str: 提取的文本内容
        """
        try:
            print("DEBUG: 尝试使用mammoth处理.doc文件")
            # 尝试使用mammoth直接处理
            with open(file_path, "rb") as docx_file:
                result = mammoth.extract_raw_text(docx_file)
                if result.value:
                    print(f"DEBUG: mammoth成功提取文本，长度: {len(result.value)}")
                    return result.value
                else:
                    raise Exception("mammoth未能提取到文本内容")

        except Exception as mammoth_error:
            print(f"DEBUG: mammoth处理失败: {mammoth_error}")

            # 如果mammoth失败，尝试强制使用python-docx
            try:
                print("DEBUG: 强制使用python-docx处理.doc文件")
                doc = docx.Document(file_path)

                # 提取所有段落文本
                paragraphs = []
                for paragraph in doc.paragraphs:
                    text = paragraph.text.strip()
                    if text:
                        paragraphs.append(text)

                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            if cell_text:
                                row_text.append(cell_text)
                        if row_text:
                            paragraphs.append(" | ".join(row_text))

                result_text = "\n\n".join(paragraphs)
                if result_text:
                    print(f"DEBUG: python-docx成功提取文本，长度: {len(result_text)}")
                    return result_text
                else:
                    raise Exception("python-docx未能提取到文本内容")

            except Exception as docx_error:
                print(f"DEBUG: python-docx也失败了: {docx_error}")

                # 最后尝试：返回基本的错误信息和文件信息
                file_size = os.path.getsize(file_path)
                raise Exception(f"无法解析.doc文件 (文件大小: {file_size} bytes)。可能的原因：1) 文件损坏 2) 文件格式不正确 3) 文件被加密或受保护。mammoth错误: {mammoth_error}; python-docx错误: {docx_error}")
    
    async def _convert_to_html(self, file_path: str) -> str:
        """
        使用mammoth转换为HTML格式

        Args:
            file_path: 文件路径

        Returns:
            str: HTML内容
        """
        try:
            print(f"DEBUG: 开始HTML转换: {file_path}")

            with open(file_path, "rb") as docx_file:
                result = mammoth.convert_to_html(docx_file)

                # 检查转换警告
                if result.messages:
                    print(f"DEBUG: Mammoth转换警告: {result.messages}")

                if not result.value or len(result.value.strip()) < 10:
                    print("DEBUG: mammoth转换结果为空或太短，尝试备用方案")
                    # 如果mammoth转换失败，使用文本内容创建简单HTML
                    text_content = self._extract_text_content(file_path)
                    html_content = self._text_to_html(text_content)
                else:
                    html_content = result.value
                    print(f"DEBUG: mammoth转换成功，HTML长度: {len(html_content)}")

                # 添加基础CSS样式
                html_with_style = self._add_html_styles(html_content)

                return html_with_style

        except Exception as e:
            print(f"DEBUG: HTML转换异常: {str(e)}")
            # 如果HTML转换完全失败，尝试从文本创建HTML
            try:
                print("DEBUG: 尝试从文本内容创建HTML")
                text_content = self._extract_text_content(file_path)
                html_content = self._text_to_html(text_content)
                return self._add_html_styles(html_content)
            except Exception as text_error:
                raise Exception(f"HTML转换失败: {str(e)}; 文本转换也失败: {str(text_error)}")

    def _text_to_html(self, text_content: str) -> str:
        """
        将纯文本转换为简单的HTML格式

        Args:
            text_content: 纯文本内容

        Returns:
            str: HTML内容
        """
        if not text_content:
            return "<p>文档内容为空</p>"

        # 将文本按段落分割并转换为HTML
        paragraphs = text_content.split('\n\n')
        html_paragraphs = []

        for para in paragraphs:
            para = para.strip()
            if para:
                # 简单的HTML转义
                para = para.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                # 处理表格格式（包含 | 分隔符的行）
                if ' | ' in para:
                    cells = para.split(' | ')
                    table_row = '<tr>' + ''.join(f'<td>{cell}</td>' for cell in cells) + '</tr>'
                    html_paragraphs.append(f'<table border="1"><tbody>{table_row}</tbody></table>')
                else:
                    html_paragraphs.append(f'<p>{para}</p>')

        return '\n'.join(html_paragraphs)
    
    def _extract_document_structure(self, file_path: str) -> Dict:
        """
        提取文档结构信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文档结构信息
        """
        try:
            doc = docx.Document(file_path)
            
            structure = {
                "paragraphs": [],
                "headings": [],
                "tables": [],
                "total_paragraphs": 0,
                "total_tables": 0
            }
            
            # 分析段落和标题
            for i, paragraph in enumerate(doc.paragraphs):
                text = paragraph.text.strip()
                if not text:
                    continue
                
                para_info = {
                    "index": i,
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "style": paragraph.style.name if paragraph.style else "Normal",
                    "is_heading": False
                }
                
                # 检查是否为标题
                if paragraph.style and "Heading" in paragraph.style.name:
                    para_info["is_heading"] = True
                    structure["headings"].append(para_info)
                
                structure["paragraphs"].append(para_info)
            
            # 分析表格
            for i, table in enumerate(doc.tables):
                table_info = {
                    "index": i,
                    "rows": len(table.rows),
                    "columns": len(table.columns) if table.rows else 0,
                    "preview": self._get_table_preview(table)
                }
                structure["tables"].append(table_info)
            
            structure["total_paragraphs"] = len(structure["paragraphs"])
            structure["total_tables"] = len(structure["tables"])
            
            return structure
            
        except Exception as e:
            raise Exception(f"提取文档结构失败: {str(e)}")
    
    def _get_document_statistics(self, file_path: str) -> Dict:
        """
        获取文档统计信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 统计信息
        """
        try:
            doc = docx.Document(file_path)
            
            # 统计字符和段落
            total_chars = 0
            total_words = 0
            total_paragraphs = 0
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    total_paragraphs += 1
                    total_chars += len(text)
                    total_words += len(text.split())
            
            # 文件大小
            file_size = os.path.getsize(file_path)
            
            return {
                "file_size": file_size,
                "total_characters": total_chars,
                "total_words": total_words,
                "total_paragraphs": total_paragraphs,
                "total_tables": len(doc.tables),
                "estimated_pages": max(1, total_words // 250)  # 估算页数
            }
            
        except Exception as e:
            return {"error": f"统计信息获取失败: {str(e)}"}
    
    def _add_html_styles(self, html_content: str) -> str:
        """
        为HTML内容添加样式
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            str: 带样式的HTML内容
        """
        styles = """
        <style>
            body { 
                font-family: 'Microsoft YaHei', Arial, sans-serif; 
                line-height: 1.6; 
                margin: 20px; 
                color: #333;
            }
            p { margin: 10px 0; }
            h1, h2, h3, h4, h5, h6 { 
                color: #2c3e50; 
                margin: 20px 0 10px 0; 
            }
            table { 
                border-collapse: collapse; 
                width: 100%; 
                margin: 15px 0; 
            }
            td, th { 
                border: 1px solid #ddd; 
                padding: 8px; 
                text-align: left; 
            }
            th { background-color: #f2f2f2; }
            .highlight { 
                background-color: rgba(255, 255, 0, 0.4); 
                border: 2px solid #ff4444; 
                border-radius: 3px; 
                padding: 2px; 
            }
        </style>
        """
        
        return f"<!DOCTYPE html><html><head>{styles}</head><body>{html_content}</body></html>"
    
    def _get_table_preview(self, table) -> str:
        """
        获取表格预览文本
        
        Args:
            table: docx表格对象
            
        Returns:
            str: 表格预览
        """
        try:
            if not table.rows:
                return "空表格"
            
            # 获取第一行作为预览
            first_row = table.rows[0]
            cells = [cell.text.strip() for cell in first_row.cells]
            preview = " | ".join(cells[:3])  # 只显示前3列
            
            if len(cells) > 3:
                preview += "..."
            
            return preview[:100] + "..." if len(preview) > 100 else preview
            
        except Exception:
            return "表格预览获取失败"
