"""
Word文档解析服务
使用python-docx和mammoth处理Word文档
"""

import os
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path

import docx
import mammoth
from docx.document import Document
from docx.text.paragraph import Paragraph

from app.config import settings
from app.utils.text_locator import TextLocator

class DocumentParser:
    """Word文档解析器"""
    
    def __init__(self):
        self.text_locator = TextLocator()
    
    async def parse_document(self, session_id: str, filename: str) -> Dict:
        """
        解析Word文档
        
        Args:
            session_id: 会话ID
            filename: 文件名
            
        Returns:
            Dict: 解析结果
        """
        try:
            # 构建文件路径
            file_path = os.path.join(settings.temp_dir, session_id, filename)
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 提取纯文本内容
            text_content = self._extract_text_content(file_path)
            
            # 转换为HTML格式
            html_content = await self._convert_to_html(file_path)
            
            # 提取文档结构信息
            document_structure = self._extract_document_structure(file_path)
            
            # 建立文本位置映射
            text_mapping = self.text_locator.create_text_mapping(
                text_content, html_content, document_structure
            )
            
            # 获取文档统计信息
            doc_stats = self._get_document_statistics(file_path)
            
            return {
                "success": True,
                "session_id": session_id,
                "filename": filename,
                "text_content": text_content,
                "html_content": html_content,
                "document_structure": document_structure,
                "text_mapping": text_mapping,
                "statistics": doc_stats,
                "message": "文档解析成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "session_id": session_id,
                "filename": filename,
                "error": str(e),
                "message": "文档解析失败"
            }
    
    def _extract_text_content(self, file_path: str) -> str:
        """
        使用python-docx提取纯文本内容

        Args:
            file_path: 文件路径

        Returns:
            str: 纯文本内容
        """
        try:
            doc = docx.Document(file_path)
            
            # 提取所有段落文本
            paragraphs = []
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:  # 只保留非空段落
                    paragraphs.append(text)
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                    if row_text:
                        paragraphs.append(" | ".join(row_text))
            
            return "\n\n".join(paragraphs)
            
        except Exception as e:
            raise Exception(f"提取文本内容失败: {str(e)}")
    
    async def _convert_to_html(self, file_path: str) -> str:
        """
        使用mammoth转换为HTML格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: HTML内容
        """
        try:
            with open(file_path, "rb") as docx_file:
                result = mammoth.convert_to_html(docx_file)
                
                # 检查转换警告
                if result.messages:
                    print(f"Mammoth转换警告: {result.messages}")
                
                # 添加基础CSS样式
                html_with_style = self._add_html_styles(result.value)
                
                return html_with_style
                
        except Exception as e:
            raise Exception(f"HTML转换失败: {str(e)}")
    
    def _extract_document_structure(self, file_path: str) -> Dict:
        """
        提取文档结构信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文档结构信息
        """
        try:
            doc = docx.Document(file_path)
            
            structure = {
                "paragraphs": [],
                "headings": [],
                "tables": [],
                "total_paragraphs": 0,
                "total_tables": 0
            }
            
            # 分析段落和标题
            for i, paragraph in enumerate(doc.paragraphs):
                text = paragraph.text.strip()
                if not text:
                    continue
                
                para_info = {
                    "index": i,
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "style": paragraph.style.name if paragraph.style else "Normal",
                    "is_heading": False
                }
                
                # 检查是否为标题
                if paragraph.style and "Heading" in paragraph.style.name:
                    para_info["is_heading"] = True
                    structure["headings"].append(para_info)
                
                structure["paragraphs"].append(para_info)
            
            # 分析表格
            for i, table in enumerate(doc.tables):
                table_info = {
                    "index": i,
                    "rows": len(table.rows),
                    "columns": len(table.columns) if table.rows else 0,
                    "preview": self._get_table_preview(table)
                }
                structure["tables"].append(table_info)
            
            structure["total_paragraphs"] = len(structure["paragraphs"])
            structure["total_tables"] = len(structure["tables"])
            
            return structure
            
        except Exception as e:
            raise Exception(f"提取文档结构失败: {str(e)}")
    
    def _get_document_statistics(self, file_path: str) -> Dict:
        """
        获取文档统计信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 统计信息
        """
        try:
            doc = docx.Document(file_path)
            
            # 统计字符和段落
            total_chars = 0
            total_words = 0
            total_paragraphs = 0
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    total_paragraphs += 1
                    total_chars += len(text)
                    total_words += len(text.split())
            
            # 文件大小
            file_size = os.path.getsize(file_path)
            
            return {
                "file_size": file_size,
                "total_characters": total_chars,
                "total_words": total_words,
                "total_paragraphs": total_paragraphs,
                "total_tables": len(doc.tables),
                "estimated_pages": max(1, total_words // 250)  # 估算页数
            }
            
        except Exception as e:
            return {"error": f"统计信息获取失败: {str(e)}"}
    
    def _add_html_styles(self, html_content: str) -> str:
        """
        为HTML内容添加样式
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            str: 带样式的HTML内容
        """
        styles = """
        <style>
            body { 
                font-family: 'Microsoft YaHei', Arial, sans-serif; 
                line-height: 1.6; 
                margin: 20px; 
                color: #333;
            }
            p { margin: 10px 0; }
            h1, h2, h3, h4, h5, h6 { 
                color: #2c3e50; 
                margin: 20px 0 10px 0; 
            }
            table { 
                border-collapse: collapse; 
                width: 100%; 
                margin: 15px 0; 
            }
            td, th { 
                border: 1px solid #ddd; 
                padding: 8px; 
                text-align: left; 
            }
            th { background-color: #f2f2f2; }
            .highlight { 
                background-color: rgba(255, 255, 0, 0.4); 
                border: 2px solid #ff4444; 
                border-radius: 3px; 
                padding: 2px; 
            }
        </style>
        """
        
        return f"<!DOCTYPE html><html><head>{styles}</head><body>{html_content}</body></html>"
    
    def _get_table_preview(self, table) -> str:
        """
        获取表格预览文本
        
        Args:
            table: docx表格对象
            
        Returns:
            str: 表格预览
        """
        try:
            if not table.rows:
                return "空表格"
            
            # 获取第一行作为预览
            first_row = table.rows[0]
            cells = [cell.text.strip() for cell in first_row.cells]
            preview = " | ".join(cells[:3])  # 只显示前3列
            
            if len(cells) > 3:
                preview += "..."
            
            return preview[:100] + "..." if len(preview) > 100 else preview
            
        except Exception:
            return "表格预览获取失败"
