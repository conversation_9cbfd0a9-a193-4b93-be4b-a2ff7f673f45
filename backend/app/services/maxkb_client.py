"""
MaxKB智能体API客户端
封装与两个MaxKB智能体的HTTP通信逻辑
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.config import settings
from app.models.maxkb_schemas import (
    MaxKBRequest,
    MaxKBResponse,
    ReviewResult,
    ExtractionResult,
    AnomalyItem,
    ExtractionItem
)

logger = logging.getLogger(__name__)

class MaxKBClient:
    """MaxKB智能体客户端"""
    
    def __init__(self):
        # 检测智能体配置
        self.review_base_url = settings.maxkb_review_base_url
        self.review_api_key = settings.maxkb_review_api_key

        # 提取智能体配置
        self.extract_base_url = settings.maxkb_extract_base_url
        self.extract_api_key = settings.maxkb_extract_api_key

        self.timeout = settings.maxkb_timeout

        # 测试模式：使用真实API
        self.test_mode = getattr(settings, 'maxkb_test_mode', False)
        
        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "limits": httpx.Limits(max_keepalive_connections=5, max_connections=10),
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }
        }
    
    async def test_api_connection(self) -> Dict[str, Any]:
        """
        测试MaxKB API连接

        Returns:
            Dict: 测试结果
        """
        test_results = {
            "review_agent": {"status": "未测试", "error": None},
            "extract_agent": {"status": "未测试", "error": None}
        }

        # 测试检测智能体
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.review_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            test_data = {
                "message": "测试连接",
                "stream": False
            }

            async with httpx.AsyncClient(timeout=httpx.Timeout(30)) as client:
                response = await client.post(
                    f"{self.review_base_url}/chat",
                    json=test_data,
                    headers=headers
                )

                if response.status_code == 200:
                    test_results["review_agent"]["status"] = "连接成功"
                    logger.info("检测智能体连接测试成功")
                else:
                    test_results["review_agent"]["status"] = f"连接失败: {response.status_code}"
                    test_results["review_agent"]["error"] = response.text

        except Exception as e:
            test_results["review_agent"]["status"] = "连接失败"
            test_results["review_agent"]["error"] = str(e)
            logger.error(f"检测智能体连接测试失败: {str(e)}")

        # 测试提取智能体
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.extract_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            test_data = {
                "message": "测试连接",
                "stream": False
            }

            async with httpx.AsyncClient(timeout=httpx.Timeout(30)) as client:
                response = await client.post(
                    f"{self.extract_base_url}/chat",
                    json=test_data,
                    headers=headers
                )

                if response.status_code == 200:
                    test_results["extract_agent"]["status"] = "连接成功"
                    logger.info("提取智能体连接测试成功")
                else:
                    test_results["extract_agent"]["status"] = f"连接失败: {response.status_code}"
                    test_results["extract_agent"]["error"] = response.text

        except Exception as e:
            test_results["extract_agent"]["status"] = "连接失败"
            test_results["extract_agent"]["error"] = str(e)
            logger.error(f"提取智能体连接测试失败: {str(e)}")

        return test_results

    async def process_document_with_agents(self, session_id: str, document_text: str) -> Dict[str, Any]:
        """
        使用两个智能体并行处理文档
        
        Args:
            session_id: 会话ID
            document_text: 文档文本内容
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理文档，会话ID: {session_id}")
            
            # 并行调用两个智能体
            review_task = self.call_review_agent(session_id, document_text)
            extract_task = self.call_extract_agent(session_id, document_text)
            
            # 等待两个任务完成
            review_result, extract_result = await asyncio.gather(
                review_task, extract_task, return_exceptions=True
            )
            
            # 处理结果
            result = {
                "session_id": session_id,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "review_result": None,
                "extract_result": None,
                "errors": []
            }
            
            # 处理审查结果
            if isinstance(review_result, Exception):
                logger.error(f"审查智能体调用失败: {str(review_result)}")
                result["errors"].append(f"审查失败: {str(review_result)}")
            else:
                result["review_result"] = review_result
                logger.info(f"审查智能体调用成功，发现 {len(review_result.get('anomalies', []))} 个异常")
            
            # 处理提取结果
            if isinstance(extract_result, Exception):
                logger.error(f"提取智能体调用失败: {str(extract_result)}")
                result["errors"].append(f"提取失败: {str(extract_result)}")
            else:
                result["extract_result"] = extract_result
                logger.info(f"提取智能体调用成功，提取 {len(extract_result.get('extractions', []))} 个数据点")
            
            # 如果两个都失败，标记为失败
            if len(result["errors"]) == 2:
                result["success"] = False
                result["message"] = "智能体调用全部失败"
            elif len(result["errors"]) == 1:
                result["message"] = "部分智能体调用失败"
            else:
                result["message"] = "智能体调用成功"
            
            return result
            
        except Exception as e:
            logger.error(f"处理文档时发生异常: {str(e)}", exc_info=True)
            return {
                "session_id": session_id,
                "success": False,
                "message": f"处理失败: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "errors": [str(e)]
            }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.TimeoutException))
    )
    async def call_review_agent(self, session_id: str, document_text: str) -> Dict[str, Any]:
        """
        调用审查智能体
        
        Args:
            session_id: 会话ID
            document_text: 文档文本
            
        Returns:
            Dict: 审查结果
        """
        try:
            logger.info(f"调用审查智能体，会话ID: {session_id}")

            # 测试模式：返回模拟数据
            if self.test_mode:
                logger.info("使用测试模式，返回模拟审查数据")
                await asyncio.sleep(1)  # 模拟网络延迟
                return self._get_mock_review_result()

            # 构建请求数据
            request_data = {
                "message": self._build_review_prompt(document_text),
                "stream": False
            }

            # 构建请求头，包含API Key
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.review_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            # 发送HTTP请求到检测智能体
            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                response = await client.post(
                    f"{self.review_base_url}/chat",
                    json=request_data,
                    headers=headers
                )
                response.raise_for_status()
                
                # 解析响应
                response_data = response.json()
                
                # 解析审查结果
                return self._parse_review_response(response_data)
                
        except httpx.HTTPStatusError as e:
            logger.error(f"审查智能体HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"审查智能体HTTP错误: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"审查智能体请求错误: {str(e)}")
            raise Exception(f"审查智能体网络错误: {str(e)}")
        except Exception as e:
            logger.error(f"审查智能体调用异常: {str(e)}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.TimeoutException))
    )
    async def call_extract_agent(self, session_id: str, document_text: str) -> Dict[str, Any]:
        """
        调用提取智能体
        
        Args:
            session_id: 会话ID
            document_text: 文档文本
            
        Returns:
            Dict: 提取结果
        """
        try:
            logger.info(f"调用提取智能体，会话ID: {session_id}")

            # 测试模式：返回模拟数据
            if self.test_mode:
                logger.info("使用测试模式，返回模拟提取数据")
                await asyncio.sleep(1.5)  # 模拟网络延迟
                return self._get_mock_extract_result()

            # 构建请求数据
            request_data = {
                "message": self._build_extract_prompt(document_text),
                "stream": False
            }

            # 构建请求头，包含API Key
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.extract_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            # 发送HTTP请求到提取智能体
            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                response = await client.post(
                    f"{self.extract_base_url}/chat",
                    json=request_data,
                    headers=headers
                )
                response.raise_for_status()
                
                # 解析响应
                response_data = response.json()
                
                # 解析提取结果
                return self._parse_extract_response(response_data)
                
        except httpx.HTTPStatusError as e:
            logger.error(f"提取智能体HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"提取智能体HTTP错误: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"提取智能体请求错误: {str(e)}")
            raise Exception(f"提取智能体网络错误: {str(e)}")
        except Exception as e:
            logger.error(f"提取智能体调用异常: {str(e)}")
            raise
    
    def _build_review_prompt(self, document_text: str) -> str:
        """
        构建审查智能体的提示词
        
        Args:
            document_text: 文档文本
            
        Returns:
            str: 提示词
        """
        prompt = f"""
请对以下招标文件进行智能审查，识别可能存在的异常和问题：

文档内容：
{document_text[:2000]}...

请按照以下JSON格式返回审查结果：
{{
    "anomalies": [
        {{
            "id": "异常唯一标识",
            "type": "异常类型",
            "level": "critical|warning|info",
            "title": "异常标题",
            "description": "详细描述",
            "location": "文档位置描述",
            "suggestion": "修改建议",
            "original_text": "相关原文"
        }}
    ],
    "summary": "整体审查总结"
}}
"""
        return prompt.strip()
    
    def _build_extract_prompt(self, document_text: str) -> str:
        """
        构建提取智能体的提示词
        
        Args:
            document_text: 文档文本
            
        Returns:
            str: 提示词
        """
        prompt = f"""
请从以下招标文件中提取107个标准化数据点：

文档内容：
{document_text[:2000]}...

请按照以下JSON格式返回提取结果：
{{
    "extractions": [
        {{
            "id": "提取点唯一标识",
            "name": "数据点名称",
            "category": "分类",
            "content": "提取的内容",
            "location": "文档位置描述",
            "confidence": 0.95,
            "original_text": "相关原文"
        }}
    ],
    "summary": "提取总结"
}}
"""
        return prompt.strip()
    
    def _parse_review_response(self, response_data: Dict) -> Dict[str, Any]:
        """
        解析审查智能体响应
        
        Args:
            response_data: 响应数据
            
        Returns:
            Dict: 解析后的审查结果
        """
        try:
            # 这里需要根据实际的MaxKB API响应格式进行调整
            # 假设响应中包含message字段，其中是JSON格式的结果
            
            if "message" in response_data:
                # 尝试解析JSON响应
                try:
                    result = json.loads(response_data["message"])
                    return {
                        "anomalies": result.get("anomalies", []),
                        "summary": result.get("summary", ""),
                        "total_anomalies": len(result.get("anomalies", []))
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，创建一个默认结果
                    return {
                        "anomalies": [],
                        "summary": response_data["message"],
                        "total_anomalies": 0
                    }
            
            return {
                "anomalies": [],
                "summary": "未获取到有效审查结果",
                "total_anomalies": 0
            }
            
        except Exception as e:
            logger.error(f"解析审查响应失败: {str(e)}")
            return {
                "anomalies": [],
                "summary": f"解析失败: {str(e)}",
                "total_anomalies": 0
            }
    
    def _parse_extract_response(self, response_data: Dict) -> Dict[str, Any]:
        """
        解析提取智能体响应
        
        Args:
            response_data: 响应数据
            
        Returns:
            Dict: 解析后的提取结果
        """
        try:
            # 这里需要根据实际的MaxKB API响应格式进行调整
            
            if "message" in response_data:
                # 尝试解析JSON响应
                try:
                    result = json.loads(response_data["message"])
                    return {
                        "extractions": result.get("extractions", []),
                        "summary": result.get("summary", ""),
                        "total_extractions": len(result.get("extractions", []))
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，创建一个默认结果
                    return {
                        "extractions": [],
                        "summary": response_data["message"],
                        "total_extractions": 0
                    }
            
            return {
                "extractions": [],
                "summary": "未获取到有效提取结果",
                "total_extractions": 0
            }
            
        except Exception as e:
            logger.error(f"解析提取响应失败: {str(e)}")
            return {
                "extractions": [],
                "summary": f"解析失败: {str(e)}",
                "total_extractions": 0
            }

    def _get_mock_review_result(self) -> Dict[str, Any]:
        """
        获取模拟审查结果（用于测试）
        """
        return {
            "anomalies": [
                {
                    "id": "anomaly_001",
                    "type": "missing_info",
                    "level": "warning",
                    "title": "缺少项目预算信息",
                    "description": "招标文件中未明确说明项目预算金额",
                    "location": "第2页，项目概述部分",
                    "suggestion": "建议补充详细的项目预算说明",
                    "original_text": "项目概述...",
                    "confidence": 0.85
                },
                {
                    "id": "anomaly_002",
                    "type": "format_error",
                    "level": "info",
                    "title": "日期格式不统一",
                    "description": "文档中日期格式存在不一致",
                    "location": "第3页，时间安排部分",
                    "suggestion": "建议统一使用YYYY-MM-DD格式",
                    "original_text": "开标时间：2025年8月4日",
                    "confidence": 0.92
                }
            ],
            "summary": "发现2个潜在问题，包括1个警告级别和1个提示级别的异常",
            "total_anomalies": 2
        }

    def _get_mock_extract_result(self) -> Dict[str, Any]:
        """
        获取模拟提取结果（用于测试）
        """
        return {
            "extractions": [
                {
                    "id": "001",
                    "name": "项目名称",
                    "category": "basic_info",
                    "content": "某某工程建设项目",
                    "location": "第1页，标题部分",
                    "confidence": 0.95,
                    "original_text": "某某工程建设项目招标文件",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "002",
                    "name": "招标编号",
                    "category": "basic_info",
                    "content": "ZB2025-001",
                    "location": "第1页，编号部分",
                    "confidence": 0.98,
                    "original_text": "招标编号：ZB2025-001",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "003",
                    "name": "招标人",
                    "category": "basic_info",
                    "content": "某某建设集团有限公司",
                    "location": "第1页，招标人信息",
                    "confidence": 0.90,
                    "original_text": "招标人：某某建设集团有限公司",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "021",
                    "name": "注册资本要求",
                    "category": "bidder_requirements",
                    "content": "不少于1000万元人民币",
                    "location": "第5页，投标人资格要求",
                    "confidence": 0.88,
                    "original_text": "投标人注册资本不少于1000万元人民币",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "071",
                    "name": "付款方式",
                    "category": "commercial_terms",
                    "content": "按进度分期付款",
                    "location": "第8页，商务条款",
                    "confidence": 0.82,
                    "original_text": "付款方式：按工程进度分期付款",
                    "is_required": True,
                    "data_type": "text"
                }
            ],
            "summary": "成功提取5个关键数据点，涵盖基本信息、投标要求和商务条款",
            "total_extractions": 5
        }
