"""
MaxKB智能体API客户端
封装与两个MaxKB智能体的HTTP通信逻辑
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import re

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.config import settings
from app.models.maxkb_schemas import (
    MaxKBRequest,
    MaxKBResponse,
    ReviewResult,
    ExtractionResult,
    AnomalyItem,
    ExtractionItem
)

logger = logging.getLogger(__name__)

class MaxKBClient:
    """MaxKB智能体客户端"""
    
    def __init__(self):
        # 检测智能体配置
        self.review_base_url = settings.maxkb_review_base_url
        self.review_api_key = settings.maxkb_review_api_key

        # 提取智能体配置
        self.extract_base_url = settings.maxkb_extract_base_url
        self.extract_api_key = settings.maxkb_extract_api_key

        self.timeout = settings.maxkb_timeout

        # 测试模式：使用真实API
        self.test_mode = getattr(settings, 'maxkb_test_mode', False)
        
        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "limits": httpx.Limits(max_keepalive_connections=5, max_connections=10),
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }
        }
    
    async def test_api_connection(self) -> Dict[str, Any]:
        """
        测试MaxKB API连接

        Returns:
            Dict: 测试结果
        """
        test_results = {
            "review_agent": {"status": "未测试", "error": None},
            "extract_agent": {"status": "未测试", "error": None}
        }

        # 测试检测智能体
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.review_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            test_data = {
                "message": "测试连接",
                "stream": False
            }

            async with httpx.AsyncClient(timeout=httpx.Timeout(30)) as client:
                response = await client.post(
                    f"{self.review_base_url}/chat",
                    json=test_data,
                    headers=headers
                )

                if response.status_code == 200:
                    test_results["review_agent"]["status"] = "连接成功"
                    logger.info("检测智能体连接测试成功")
                else:
                    test_results["review_agent"]["status"] = f"连接失败: {response.status_code}"
                    test_results["review_agent"]["error"] = response.text

        except Exception as e:
            test_results["review_agent"]["status"] = "连接失败"
            test_results["review_agent"]["error"] = str(e)
            logger.error(f"检测智能体连接测试失败: {str(e)}")

        # 测试提取智能体
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.extract_api_key}",
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            test_data = {
                "message": "测试连接",
                "stream": False
            }

            async with httpx.AsyncClient(timeout=httpx.Timeout(30)) as client:
                response = await client.post(
                    f"{self.extract_base_url}/chat",
                    json=test_data,
                    headers=headers
                )

                if response.status_code == 200:
                    test_results["extract_agent"]["status"] = "连接成功"
                    logger.info("提取智能体连接测试成功")
                else:
                    test_results["extract_agent"]["status"] = f"连接失败: {response.status_code}"
                    test_results["extract_agent"]["error"] = response.text

        except Exception as e:
            test_results["extract_agent"]["status"] = "连接失败"
            test_results["extract_agent"]["error"] = str(e)
            logger.error(f"提取智能体连接测试失败: {str(e)}")

        return test_results

    async def _create_chat_session(self, agent_base_url: str, api_key: str) -> str:
        """
        创建MaxKB聊天会话

        Args:
            agent_base_url: 智能体基础URL
            api_key: API密钥

        Returns:
            str: 聊天会话ID

        Raises:
            Exception: 会话创建失败
        """
        try:
            # 从URL中提取application_id
            # URL格式: http://172.18.163.27:8080/api/application/{application_id}
            url_match = re.search(r'/application/([^/]+)$', agent_base_url)
            if not url_match:
                raise Exception(f"无法从URL中提取application_id: {agent_base_url}")

            application_id = url_match.group(1)
            base_url = agent_base_url.rsplit('/api/application', 1)[0]

            # 构建会话初始化URL
            session_url = f"{base_url}/api/application/{application_id}/chat/open"

            # 构建请求头 - 注意：使用AUTHORIZATION而非Authorization，且无Bearer前缀
            headers = {
                "AUTHORIZATION": api_key,
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            logger.info(f"创建聊天会话: {session_url}")

            async with httpx.AsyncClient(timeout=httpx.Timeout(30)) as client:
                response = await client.get(session_url, headers=headers)
                response.raise_for_status()

                response_data = response.json()
                logger.info(f"会话创建响应: {response_data}")

                # 检查响应格式
                if response_data.get("code") == 200 and "data" in response_data:
                    chat_id = response_data["data"]
                    logger.info(f"成功创建聊天会话: {chat_id}")
                    return chat_id
                else:
                    raise Exception(f"会话创建失败: {response_data}")

        except httpx.HTTPStatusError as e:
            logger.error(f"会话创建HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"会话创建HTTP错误: {e.response.status_code}")
        except Exception as e:
            logger.error(f"会话创建异常: {str(e)}")
            raise

    async def _upload_document_file(self, agent_base_url: str, api_key: str, chat_id: str,
                                  file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        上传文档文件到MaxKB

        Args:
            agent_base_url: 智能体基础URL
            api_key: API密钥
            chat_id: 聊天会话ID
            file_content: 文件二进制内容
            filename: 文件名

        Returns:
            Dict: 包含file_id和url的文件信息

        Raises:
            Exception: 文件上传失败
        """
        try:
            # 从URL中提取application_id
            url_match = re.search(r'/application/([^/]+)$', agent_base_url)
            if not url_match:
                raise Exception(f"无法从URL中提取application_id: {agent_base_url}")

            application_id = url_match.group(1)
            base_url = agent_base_url.rsplit('/api/application', 1)[0]

            # 构建文件上传URL
            upload_url = f"{base_url}/api/application/{application_id}/chat/{chat_id}/upload_file"

            # 构建请求头 - 注意：这里使用Authorization而非AUTHORIZATION
            headers = {
                "Authorization": api_key,
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            # 构建multipart/form-data请求
            files = {
                "file": (filename, file_content, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
            }

            logger.info(f"上传文件: {upload_url}, 文件名: {filename}, 大小: {len(file_content)} bytes")

            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                response = await client.post(upload_url, headers=headers, files=files)
                response.raise_for_status()

                response_data = response.json()
                logger.info(f"文件上传响应: {response_data}")

                # 返回文件信息
                return response_data

        except httpx.HTTPStatusError as e:
            logger.error(f"文件上传HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"文件上传HTTP错误: {e.response.status_code}")
        except Exception as e:
            logger.error(f"文件上传异常: {str(e)}")
            raise

    async def process_document_with_agents(self, session_id: str, document_text: str,
                                         file_content: bytes = None, filename: str = None) -> Dict[str, Any]:
        """
        使用两个智能体并行处理文档

        Args:
            session_id: 会话ID
            document_text: 文档文本内容
            file_content: 文档文件二进制内容（可选）
            filename: 文档文件名（可选）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理文档，会话ID: {session_id}, 文件名: {filename}")

            # 并行调用两个智能体
            review_task = self.call_review_agent(session_id, document_text, file_content, filename)
            extract_task = self.call_extract_agent(session_id, document_text, file_content, filename)
            
            # 等待两个任务完成
            review_result, extract_result = await asyncio.gather(
                review_task, extract_task, return_exceptions=True
            )
            
            # 处理结果
            result = {
                "session_id": session_id,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "review_result": None,
                "extract_result": None,
                "errors": []
            }
            
            # 处理审查结果
            if isinstance(review_result, Exception):
                logger.error(f"审查智能体调用失败: {str(review_result)}")
                result["errors"].append(f"审查失败: {str(review_result)}")
            else:
                result["review_result"] = review_result
                logger.info(f"审查智能体调用成功，发现 {len(review_result.get('anomalies', []))} 个异常")
            
            # 处理提取结果
            if isinstance(extract_result, Exception):
                logger.error(f"提取智能体调用失败: {str(extract_result)}")
                result["errors"].append(f"提取失败: {str(extract_result)}")
            else:
                result["extract_result"] = extract_result
                logger.info(f"提取智能体调用成功，提取 {len(extract_result.get('extractions', []))} 个数据点")
            
            # 如果两个都失败，标记为失败
            if len(result["errors"]) == 2:
                result["success"] = False
                result["message"] = "智能体调用全部失败"
            elif len(result["errors"]) == 1:
                result["message"] = "部分智能体调用失败"
            else:
                result["message"] = "智能体调用成功"
            
            return result
            
        except Exception as e:
            logger.error(f"处理文档时发生异常: {str(e)}", exc_info=True)
            return {
                "session_id": session_id,
                "success": False,
                "message": f"处理失败: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "errors": [str(e)]
            }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.TimeoutException))
    )
    async def call_review_agent(self, session_id: str, document_text: str,
                               file_content: bytes = None, filename: str = None) -> Dict[str, Any]:
        """
        调用审查智能体（三步API流程）

        Args:
            session_id: 会话ID
            document_text: 文档文本
            file_content: 文档文件二进制内容（可选）
            filename: 文档文件名（可选）

        Returns:
            Dict: 审查结果
        """
        try:
            logger.info(f"调用审查智能体，会话ID: {session_id}, 文件名: {filename}")

            # 测试模式：返回模拟数据
            if self.test_mode:
                logger.info("使用测试模式，返回模拟审查数据")
                await asyncio.sleep(1)  # 模拟网络延迟
                return self._get_mock_review_result()

            # 第一步：创建聊天会话
            chat_id = await self._create_chat_session(self.review_base_url, self.review_api_key)
            logger.info(f"审查智能体会话创建成功: {chat_id}")

            # 第二步：上传文档文件（如果提供了文件内容）
            document_list = []
            if file_content and filename:
                file_info = await self._upload_document_file(
                    self.review_base_url, self.review_api_key, chat_id, file_content, filename
                )
                logger.info(f"审查智能体文件上传成功: {file_info}")

                # 构建document_list
                if isinstance(file_info, dict) and "data" in file_info:
                    file_data = file_info["data"]
                    document_list = [{
                        "file_id": file_data.get("id", ""),
                        "url": file_data.get("url", "")
                    }]

            # 第三步：发送聊天消息
            return await self._send_chat_message(
                self.review_base_url, self.review_api_key, chat_id,
                self._build_review_prompt(document_text), document_list, "review"
            )

        except Exception as e:
            logger.error(f"审查智能体调用异常: {str(e)}")
            raise

    async def _send_chat_message(self, agent_base_url: str, api_key: str, chat_id: str,
                                message: str, document_list: List[Dict] = None, agent_type: str = "unknown") -> Dict[str, Any]:
        """
        发送聊天消息到MaxKB

        Args:
            agent_base_url: 智能体基础URL
            api_key: API密钥
            chat_id: 聊天会话ID
            message: 消息内容
            document_list: 文档列表
            agent_type: 智能体类型（用于日志）

        Returns:
            Dict: 聊天响应结果

        Raises:
            Exception: 聊天消息发送失败
        """
        try:
            # 从URL中提取application_id
            url_match = re.search(r'/application/([^/]+)$', agent_base_url)
            if not url_match:
                raise Exception(f"无法从URL中提取application_id: {agent_base_url}")

            application_id = url_match.group(1)
            base_url = agent_base_url.rsplit('/api/application', 1)[0]

            # 构建聊天消息URL
            chat_url = f"{base_url}/api/application/{application_id}/chat_message/{chat_id}"

            # 构建请求头 - 注意：使用AUTHORIZATION而非Authorization
            headers = {
                "Content-Type": "application/json",
                "AUTHORIZATION": api_key,
                "User-Agent": "HFGGZY-Document-Processor/1.0"
            }

            # 构建请求数据
            request_data = {
                "message": message,
                "stream": False
            }

            # 添加文档列表（如果有）
            if document_list:
                request_data["document_list"] = document_list

            logger.info(f"发送{agent_type}聊天消息: {chat_url}")
            logger.debug(f"请求数据: {request_data}")

            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                response = await client.post(chat_url, headers=headers, json=request_data)
                response.raise_for_status()

                response_data = response.json()
                logger.info(f"{agent_type}聊天响应: {response_data}")

                # 解析响应
                if agent_type == "review":
                    return self._parse_review_response(response_data)
                elif agent_type == "extract":
                    return self._parse_extract_response(response_data)
                else:
                    return response_data

        except httpx.HTTPStatusError as e:
            logger.error(f"{agent_type}聊天消息HTTP错误: {e.response.status_code} - {e.response.text}")
            raise Exception(f"{agent_type}聊天消息HTTP错误: {e.response.status_code}")
        except Exception as e:
            logger.error(f"{agent_type}聊天消息异常: {str(e)}")
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.TimeoutException))
    )
    async def call_extract_agent(self, session_id: str, document_text: str,
                                file_content: bytes = None, filename: str = None) -> Dict[str, Any]:
        """
        调用提取智能体（三步API流程）

        Args:
            session_id: 会话ID
            document_text: 文档文本
            file_content: 文档文件二进制内容（可选）
            filename: 文档文件名（可选）

        Returns:
            Dict: 提取结果
        """
        try:
            logger.info(f"调用提取智能体，会话ID: {session_id}, 文件名: {filename}")

            # 测试模式：返回模拟数据
            if self.test_mode:
                logger.info("使用测试模式，返回模拟提取数据")
                await asyncio.sleep(1.5)  # 模拟网络延迟
                return self._get_mock_extract_result()

            # 第一步：创建聊天会话
            chat_id = await self._create_chat_session(self.extract_base_url, self.extract_api_key)
            logger.info(f"提取智能体会话创建成功: {chat_id}")

            # 第二步：上传文档文件（如果提供了文件内容）
            document_list = []
            if file_content and filename:
                file_info = await self._upload_document_file(
                    self.extract_base_url, self.extract_api_key, chat_id, file_content, filename
                )
                logger.info(f"提取智能体文件上传成功: {file_info}")

                # 构建document_list
                if isinstance(file_info, dict) and "data" in file_info:
                    file_data = file_info["data"]
                    document_list = [{
                        "file_id": file_data.get("id", ""),
                        "url": file_data.get("url", "")
                    }]

            # 第三步：发送聊天消息
            return await self._send_chat_message(
                self.extract_base_url, self.extract_api_key, chat_id,
                self._build_extract_prompt(document_text), document_list, "extract"
            )

        except Exception as e:
            logger.error(f"提取智能体调用异常: {str(e)}")
            raise
    
    def _build_review_prompt(self, document_text: str) -> str:
        """
        构建审查智能体的提示词
        
        Args:
            document_text: 文档文本
            
        Returns:
            str: 提示词
        """
        prompt = f"""
请对以下招标文件进行智能审查，识别可能存在的异常和问题：

文档内容：
{document_text[:2000]}...

请按照以下JSON格式返回审查结果：
{{
    "anomalies": [
        {{
            "id": "异常唯一标识",
            "type": "异常类型",
            "level": "critical|warning|info",
            "title": "异常标题",
            "description": "详细描述",
            "location": "文档位置描述",
            "suggestion": "修改建议",
            "original_text": "相关原文"
        }}
    ],
    "summary": "整体审查总结"
}}
"""
        return prompt.strip()
    
    def _build_extract_prompt(self, document_text: str) -> str:
        """
        构建提取智能体的提示词
        
        Args:
            document_text: 文档文本
            
        Returns:
            str: 提示词
        """
        prompt = f"""
请从以下招标文件中提取107个标准化数据点：

文档内容：
{document_text[:2000]}...

请按照以下JSON格式返回提取结果：
{{
    "extractions": [
        {{
            "id": "提取点唯一标识",
            "name": "数据点名称",
            "category": "分类",
            "content": "提取的内容",
            "location": "文档位置描述",
            "confidence": 0.95,
            "original_text": "相关原文"
        }}
    ],
    "summary": "提取总结"
}}
"""
        return prompt.strip()
    
    def _parse_review_response(self, response_data: Dict) -> Dict[str, Any]:
        """
        解析审查智能体响应
        
        Args:
            response_data: 响应数据
            
        Returns:
            Dict: 解析后的审查结果
        """
        try:
            # 这里需要根据实际的MaxKB API响应格式进行调整
            # 假设响应中包含message字段，其中是JSON格式的结果
            
            if "message" in response_data:
                # 尝试解析JSON响应
                try:
                    result = json.loads(response_data["message"])
                    return {
                        "anomalies": result.get("anomalies", []),
                        "summary": result.get("summary", ""),
                        "total_anomalies": len(result.get("anomalies", []))
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，创建一个默认结果
                    return {
                        "anomalies": [],
                        "summary": response_data["message"],
                        "total_anomalies": 0
                    }
            
            return {
                "anomalies": [],
                "summary": "未获取到有效审查结果",
                "total_anomalies": 0
            }
            
        except Exception as e:
            logger.error(f"解析审查响应失败: {str(e)}")
            return {
                "anomalies": [],
                "summary": f"解析失败: {str(e)}",
                "total_anomalies": 0
            }
    
    def _parse_extract_response(self, response_data: Dict) -> Dict[str, Any]:
        """
        解析提取智能体响应
        
        Args:
            response_data: 响应数据
            
        Returns:
            Dict: 解析后的提取结果
        """
        try:
            # 这里需要根据实际的MaxKB API响应格式进行调整
            
            if "message" in response_data:
                # 尝试解析JSON响应
                try:
                    result = json.loads(response_data["message"])
                    return {
                        "extractions": result.get("extractions", []),
                        "summary": result.get("summary", ""),
                        "total_extractions": len(result.get("extractions", []))
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，创建一个默认结果
                    return {
                        "extractions": [],
                        "summary": response_data["message"],
                        "total_extractions": 0
                    }
            
            return {
                "extractions": [],
                "summary": "未获取到有效提取结果",
                "total_extractions": 0
            }
            
        except Exception as e:
            logger.error(f"解析提取响应失败: {str(e)}")
            return {
                "extractions": [],
                "summary": f"解析失败: {str(e)}",
                "total_extractions": 0
            }

    def _get_mock_review_result(self) -> Dict[str, Any]:
        """
        获取模拟审查结果（用于测试）
        """
        return {
            "anomalies": [
                {
                    "id": "anomaly_001",
                    "type": "missing_info",
                    "level": "warning",
                    "title": "缺少项目预算信息",
                    "description": "招标文件中未明确说明项目预算金额",
                    "location": "第2页，项目概述部分",
                    "suggestion": "建议补充详细的项目预算说明",
                    "original_text": "项目概述...",
                    "confidence": 0.85
                },
                {
                    "id": "anomaly_002",
                    "type": "format_error",
                    "level": "info",
                    "title": "日期格式不统一",
                    "description": "文档中日期格式存在不一致",
                    "location": "第3页，时间安排部分",
                    "suggestion": "建议统一使用YYYY-MM-DD格式",
                    "original_text": "开标时间：2025年8月4日",
                    "confidence": 0.92
                }
            ],
            "summary": "发现2个潜在问题，包括1个警告级别和1个提示级别的异常",
            "total_anomalies": 2
        }

    def _get_mock_extract_result(self) -> Dict[str, Any]:
        """
        获取模拟提取结果（用于测试）
        """
        return {
            "extractions": [
                {
                    "id": "001",
                    "name": "项目名称",
                    "category": "basic_info",
                    "content": "某某工程建设项目",
                    "location": "第1页，标题部分",
                    "confidence": 0.95,
                    "original_text": "某某工程建设项目招标文件",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "002",
                    "name": "招标编号",
                    "category": "basic_info",
                    "content": "ZB2025-001",
                    "location": "第1页，编号部分",
                    "confidence": 0.98,
                    "original_text": "招标编号：ZB2025-001",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "003",
                    "name": "招标人",
                    "category": "basic_info",
                    "content": "某某建设集团有限公司",
                    "location": "第1页，招标人信息",
                    "confidence": 0.90,
                    "original_text": "招标人：某某建设集团有限公司",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "021",
                    "name": "注册资本要求",
                    "category": "bidder_requirements",
                    "content": "不少于1000万元人民币",
                    "location": "第5页，投标人资格要求",
                    "confidence": 0.88,
                    "original_text": "投标人注册资本不少于1000万元人民币",
                    "is_required": True,
                    "data_type": "text"
                },
                {
                    "id": "071",
                    "name": "付款方式",
                    "category": "commercial_terms",
                    "content": "按进度分期付款",
                    "location": "第8页，商务条款",
                    "confidence": 0.82,
                    "original_text": "付款方式：按工程进度分期付款",
                    "is_required": True,
                    "data_type": "text"
                }
            ],
            "summary": "成功提取5个关键数据点，涵盖基本信息、投标要求和商务条款",
            "total_extractions": 5
        }
