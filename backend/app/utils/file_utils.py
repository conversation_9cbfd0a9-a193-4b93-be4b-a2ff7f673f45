"""
文件操作工具函数
处理文件验证、存储和管理
"""

import os
import aiofiles
from typing import Optional
from pathlib import Path

from app.config import settings

def validate_file_type(filename: Optional[str]) -> bool:
    """
    验证文件类型是否被支持
    
    Args:
        filename: 文件名
        
    Returns:
        bool: 是否为支持的文件类型
    """
    if not filename:
        return False
    
    # 获取文件扩展名（转为小写）
    file_ext = Path(filename).suffix.lower()
    
    # 检查是否在允许的扩展名列表中
    return file_ext in [ext.lower() for ext in settings.allowed_extensions]

def validate_file_size(file_size: int) -> bool:
    """
    验证文件大小是否在限制范围内
    
    Args:
        file_size: 文件大小（字节）
        
    Returns:
        bool: 是否在大小限制内
    """
    return 0 < file_size <= settings.max_file_size

def create_session_directory(session_id: str) -> str:
    """
    创建会话临时目录
    
    Args:
        session_id: 会话ID
        
    Returns:
        str: 创建的目录路径
        
    Raises:
        OSError: 目录创建失败
    """
    session_dir = os.path.join(settings.temp_dir, session_id)
    
    try:
        os.makedirs(session_dir, exist_ok=True)
        return session_dir
    except OSError as e:
        raise OSError(f"创建会话目录失败: {str(e)}")

async def save_uploaded_file(file_content: bytes, filename: str, session_dir: str) -> str:
    """
    保存上传的文件到指定目录
    
    Args:
        file_content: 文件内容（字节）
        filename: 原始文件名
        session_dir: 会话目录路径
        
    Returns:
        str: 保存的文件路径
        
    Raises:
        IOError: 文件保存失败
    """
    try:
        # 构建文件保存路径
        file_path = os.path.join(session_dir, filename)
        
        # 异步写入文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        return file_path
        
    except Exception as e:
        raise IOError(f"保存文件失败: {str(e)}")

def get_file_info(file_path: str) -> dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 文件信息字典
    """
    try:
        if not os.path.exists(file_path):
            return {"exists": False}
        
        stat = os.stat(file_path)
        
        return {
            "exists": True,
            "size": stat.st_size,
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "filename": os.path.basename(file_path),
            "extension": Path(file_path).suffix.lower()
        }
        
    except Exception as e:
        return {"exists": False, "error": str(e)}

def cleanup_old_sessions(max_age_hours: int = 24) -> int:
    """
    清理超过指定时间的会话目录
    
    Args:
        max_age_hours: 最大保留时间（小时）
        
    Returns:
        int: 清理的目录数量
    """
    import time
    import shutil
    
    cleaned_count = 0
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    try:
        if not os.path.exists(settings.temp_dir):
            return 0
        
        for item in os.listdir(settings.temp_dir):
            item_path = os.path.join(settings.temp_dir, item)
            
            # 跳过非目录项
            if not os.path.isdir(item_path):
                continue
            
            # 检查目录创建时间
            dir_stat = os.stat(item_path)
            if current_time - dir_stat.st_ctime > max_age_seconds:
                try:
                    shutil.rmtree(item_path)
                    cleaned_count += 1
                except Exception:
                    # 忽略删除失败的情况
                    pass
        
        return cleaned_count
        
    except Exception:
        return 0

def get_session_files(session_id: str) -> list:
    """
    获取会话目录中的所有文件
    
    Args:
        session_id: 会话ID
        
    Returns:
        list: 文件信息列表
    """
    session_dir = os.path.join(settings.temp_dir, session_id)
    files = []
    
    try:
        if not os.path.exists(session_dir):
            return files
        
        for filename in os.listdir(session_dir):
            file_path = os.path.join(session_dir, filename)
            if os.path.isfile(file_path):
                file_info = get_file_info(file_path)
                files.append(file_info)
        
        return files
        
    except Exception:
        return files
