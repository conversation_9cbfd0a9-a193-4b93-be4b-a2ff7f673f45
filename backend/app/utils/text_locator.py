"""
文本定位算法实现
用于在HTML文档中精确定位文本内容
"""

import re
import difflib
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup

class TextLocator:
    """文本定位器"""
    
    def __init__(self):
        self.similarity_threshold = 0.8  # 相似度阈值
        self.context_window = 50  # 上下文窗口大小
    
    def create_text_mapping(self, text_content: str, html_content: str, 
                          document_structure: Dict) -> Dict:
        """
        创建文本内容与HTML位置的映射关系
        
        Args:
            text_content: 纯文本内容
            html_content: HTML内容
            document_structure: 文档结构信息
            
        Returns:
            Dict: 文本映射信息
        """
        try:
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取HTML中的文本段落
            html_paragraphs = self._extract_html_paragraphs(soup)
            
            # 分割纯文本为段落
            text_paragraphs = self._split_text_paragraphs(text_content)
            
            # 创建段落映射
            paragraph_mapping = self._create_paragraph_mapping(
                text_paragraphs, html_paragraphs
            )
            
            # 创建字符级映射
            character_mapping = self._create_character_mapping(
                text_content, soup
            )
            
            return {
                "paragraph_mapping": paragraph_mapping,
                "character_mapping": character_mapping,
                "html_paragraphs": html_paragraphs,
                "text_paragraphs": text_paragraphs,
                "total_paragraphs": len(text_paragraphs),
                "mapping_accuracy": self._calculate_mapping_accuracy(paragraph_mapping)
            }
            
        except Exception as e:
            return {
                "error": f"创建文本映射失败: {str(e)}",
                "paragraph_mapping": [],
                "character_mapping": {},
                "mapping_accuracy": 0.0
            }
    
    def locate_text_in_html(self, target_text: str, html_content: str, 
                           text_mapping: Dict) -> Dict:
        """
        在HTML中定位指定文本
        
        Args:
            target_text: 目标文本
            html_content: HTML内容
            text_mapping: 文本映射信息
            
        Returns:
            Dict: 定位结果
        """
        try:
            # 清理目标文本
            cleaned_target = self._clean_text(target_text)
            
            if len(cleaned_target) < 3:
                return {"success": False, "error": "目标文本太短"}
            
            # 在HTML中搜索
            locations = self._search_in_html(cleaned_target, html_content)
            
            # 使用映射信息优化定位
            optimized_locations = self._optimize_locations_with_mapping(
                locations, text_mapping
            )
            
            # 生成高亮脚本
            highlight_script = self._generate_highlight_script(optimized_locations)
            
            return {
                "success": True,
                "target_text": target_text,
                "locations": optimized_locations,
                "highlight_script": highlight_script,
                "total_matches": len(optimized_locations)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"文本定位失败: {str(e)}",
                "target_text": target_text
            }
    
    def _extract_html_paragraphs(self, soup: BeautifulSoup) -> List[Dict]:
        """
        从HTML中提取段落信息
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            List[Dict]: 段落信息列表
        """
        paragraphs = []
        
        # 查找所有段落元素
        for i, element in enumerate(soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div'])):
            text = element.get_text().strip()
            if text:
                paragraphs.append({
                    "index": i,
                    "text": text,
                    "tag": element.name,
                    "element_id": f"para_{i}",
                    "html": str(element)
                })
        
        return paragraphs
    
    def _split_text_paragraphs(self, text_content: str) -> List[str]:
        """
        分割纯文本为段落
        
        Args:
            text_content: 纯文本内容
            
        Returns:
            List[str]: 段落列表
        """
        # 按双换行符分割段落
        paragraphs = re.split(r'\n\s*\n', text_content)
        
        # 清理空段落
        cleaned_paragraphs = []
        for para in paragraphs:
            cleaned = para.strip()
            if cleaned:
                cleaned_paragraphs.append(cleaned)
        
        return cleaned_paragraphs
    
    def _create_paragraph_mapping(self, text_paragraphs: List[str], 
                                html_paragraphs: List[Dict]) -> List[Dict]:
        """
        创建段落映射
        
        Args:
            text_paragraphs: 文本段落列表
            html_paragraphs: HTML段落列表
            
        Returns:
            List[Dict]: 段落映射列表
        """
        mapping = []
        
        for i, text_para in enumerate(text_paragraphs):
            best_match = self._find_best_html_match(text_para, html_paragraphs)
            
            mapping.append({
                "text_index": i,
                "text_content": text_para[:100] + "..." if len(text_para) > 100 else text_para,
                "html_index": best_match["index"] if best_match else -1,
                "html_element_id": best_match["element_id"] if best_match else None,
                "similarity": best_match["similarity"] if best_match else 0.0,
                "confidence": "high" if best_match and best_match["similarity"] > 0.9 else 
                            "medium" if best_match and best_match["similarity"] > 0.7 else "low"
            })
        
        return mapping
    
    def _find_best_html_match(self, text_para: str, html_paragraphs: List[Dict]) -> Optional[Dict]:
        """
        为文本段落找到最佳的HTML匹配
        
        Args:
            text_para: 文本段落
            html_paragraphs: HTML段落列表
            
        Returns:
            Optional[Dict]: 最佳匹配信息
        """
        best_match = None
        best_similarity = 0.0
        
        cleaned_text = self._clean_text(text_para)
        
        for html_para in html_paragraphs:
            cleaned_html = self._clean_text(html_para["text"])
            
            # 计算相似度
            similarity = difflib.SequenceMatcher(None, cleaned_text, cleaned_html).ratio()
            
            if similarity > best_similarity and similarity > self.similarity_threshold:
                best_similarity = similarity
                best_match = {
                    "index": html_para["index"],
                    "element_id": html_para["element_id"],
                    "similarity": similarity,
                    "html_text": html_para["text"]
                }
        
        return best_match
    
    def _create_character_mapping(self, text_content: str, soup: BeautifulSoup) -> Dict:
        """
        创建字符级映射
        
        Args:
            text_content: 纯文本内容
            soup: BeautifulSoup对象
            
        Returns:
            Dict: 字符映射信息
        """
        # 简化的字符映射实现
        html_text = soup.get_text()
        cleaned_text = self._clean_text(text_content)
        cleaned_html = self._clean_text(html_text)
        
        # 使用序列匹配器创建基础映射
        matcher = difflib.SequenceMatcher(None, cleaned_text, cleaned_html)
        
        mapping_blocks = []
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                mapping_blocks.append({
                    "text_start": i1,
                    "text_end": i2,
                    "html_start": j1,
                    "html_end": j2,
                    "type": "match"
                })
        
        return {
            "mapping_blocks": mapping_blocks,
            "text_length": len(cleaned_text),
            "html_length": len(cleaned_html)
        }
    
    def _search_in_html(self, target_text: str, html_content: str) -> List[Dict]:
        """
        在HTML中搜索目标文本
        
        Args:
            target_text: 目标文本
            html_content: HTML内容
            
        Returns:
            List[Dict]: 搜索结果
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        locations = []
        
        # 在所有文本节点中搜索
        for element in soup.find_all(text=True):
            text = element.strip()
            if not text:
                continue
            
            # 查找所有匹配位置
            start = 0
            while True:
                pos = text.lower().find(target_text.lower(), start)
                if pos == -1:
                    break
                
                # 获取上下文
                context_start = max(0, pos - self.context_window)
                context_end = min(len(text), pos + len(target_text) + self.context_window)
                context = text[context_start:context_end]
                
                locations.append({
                    "position": pos,
                    "context": context,
                    "element": element.parent.name if element.parent else "text",
                    "full_text": text,
                    "match_text": text[pos:pos + len(target_text)]
                })
                
                start = pos + 1
        
        return locations
    
    def _optimize_locations_with_mapping(self, locations: List[Dict], 
                                       text_mapping: Dict) -> List[Dict]:
        """
        使用映射信息优化定位结果
        
        Args:
            locations: 原始定位结果
            text_mapping: 文本映射信息
            
        Returns:
            List[Dict]: 优化后的定位结果
        """
        # 简化实现：直接返回原始结果
        # 在实际应用中，可以根据映射信息进一步优化定位精度
        return locations
    
    def _generate_highlight_script(self, locations: List[Dict]) -> str:
        """
        生成高亮定位脚本
        
        Args:
            locations: 定位结果
            
        Returns:
            str: JavaScript高亮脚本
        """
        if not locations:
            return ""
        
        script = """
        function highlightText() {
            // 移除之前的高亮
            document.querySelectorAll('.highlight').forEach(el => {
                el.outerHTML = el.innerHTML;
            });
            
            // 添加新的高亮
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            textNodes.forEach(textNode => {
                const text = textNode.textContent;
                let newHTML = text;
                
                // 这里添加具体的高亮逻辑
                // 实际实现中需要根据locations进行精确匹配
                
                if (newHTML !== text) {
                    const span = document.createElement('span');
                    span.innerHTML = newHTML;
                    textNode.parentNode.replaceChild(span, textNode);
                }
            });
        }
        
        // 自动执行高亮
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', highlightText);
        } else {
            highlightText();
        }
        """
        
        return script
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本，移除多余的空白字符
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        return cleaned
    
    def _calculate_mapping_accuracy(self, paragraph_mapping: List[Dict]) -> float:
        """
        计算映射准确性
        
        Args:
            paragraph_mapping: 段落映射列表
            
        Returns:
            float: 准确性分数 (0-1)
        """
        if not paragraph_mapping:
            return 0.0
        
        high_confidence = sum(1 for m in paragraph_mapping if m["confidence"] == "high")
        total = len(paragraph_mapping)
        
        return high_confidence / total if total > 0 else 0.0
