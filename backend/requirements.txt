# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0

# HTTP 客户端·
httpx==0.25.2
requests==2.31.0
tenacity==8.2.3

# 文档处理
python-docx==1.1.0
mammoth==1.9.1
beautifulsoup4==4.12.2

# 数据验证和序列化 (使用较新版本，有预编译wheel)
pydantic==2.8.2
pydantic-settings==2.4.0

# 文件处理
python-multipart==0.0.6
aiofiles==23.2.1

# 工具库
python-dateutil==2.8.2
uuid==1.30
psutil==5.9.6

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 日志
loguru==0.7.2
