.App {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 三栏布局样式 */
.main-layout {
  height: 100%;
  display: flex;
}

.left-panel {
  width: 300px;
  min-width: 300px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.center-panel {
  flex: 1;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 300px;
  min-width: 300px;
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

/* 面板内容区域 */
.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 文档高亮样式 */
.document-highlight {
  background: rgba(255, 255, 0, 0.4);
  border: 2px solid #ff4444;
  border-radius: 3px;
  padding: 2px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 250px;
    min-width: 250px;
  }
}
