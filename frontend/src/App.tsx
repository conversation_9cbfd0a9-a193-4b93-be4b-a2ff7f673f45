import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { App as AntdApp } from 'antd'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import './App.css'

const App: React.FC = () => {
  return (
    <AntdApp>
      <div className="App">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
          </Route>
        </Routes>
      </div>
    </AntdApp>
  )
}

export default App
