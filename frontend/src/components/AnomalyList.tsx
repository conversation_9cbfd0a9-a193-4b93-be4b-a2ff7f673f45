/**
 * 异常点列表组件
 * 显示智能体检测到的文档异常
 */

import React, { useState, useMemo } from 'react';
import { Typography, Input, Select, Space, Badge, Empty, Divider } from 'antd';
import { SearchOutlined, BugOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import ListItem from './ListItem';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

interface AnomalyItem {
  id: string;
  type: string;
  level: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  location: string;
  suggestion?: string;
  original_text?: string;
  confidence?: number;
}

interface AnomalyListProps {
  anomalies: AnomalyItem[];
  loading?: boolean;
  onAnomalyClick?: (anomalyId: string) => void;
  onAnomalyCopy?: (content: string) => void;
}

const AnomalyList: React.FC<AnomalyListProps> = ({
  anomalies = [],
  loading = false,
  onAnomalyClick,
  onAnomalyCopy
}) => {
  const [searchText, setSearchText] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // 异常类型映射
  const anomalyTypeMap = {
    'missing_info': '信息缺失',
    'inconsistent_data': '数据不一致',
    'format_error': '格式错误',
    'logic_error': '逻辑错误',
    'compliance_issue': '合规问题',
    'other': '其他'
  };

  // 获取异常类型列表
  const anomalyTypes = useMemo(() => {
    const types = new Set(anomalies.map(item => item.type));
    return Array.from(types);
  }, [anomalies]);

  // 过滤异常列表
  const filteredAnomalies = useMemo(() => {
    return anomalies.filter(anomaly => {
      // 搜索过滤
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        const matchesSearch = 
          anomaly.title.toLowerCase().includes(searchLower) ||
          anomaly.description.toLowerCase().includes(searchLower) ||
          (anomaly.original_text && anomaly.original_text.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // 级别过滤
      if (levelFilter !== 'all' && anomaly.level !== levelFilter) {
        return false;
      }

      // 类型过滤
      if (typeFilter !== 'all' && anomaly.type !== typeFilter) {
        return false;
      }

      return true;
    });
  }, [anomalies, searchText, levelFilter, typeFilter]);

  // 按级别分组统计
  const levelStats = useMemo(() => {
    const stats = { critical: 0, warning: 0, info: 0 };
    anomalies.forEach(anomaly => {
      stats[anomaly.level]++;
    });
    return stats;
  }, [anomalies]);

  // 处理异常点击
  const handleAnomalyClick = (anomalyId: string) => {
    if (onAnomalyClick) {
      onAnomalyClick(anomalyId);
    }
  };

  // 处理复制
  const handleCopy = (content: string) => {
    if (onAnomalyCopy) {
      onAnomalyCopy(content);
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 标题和统计 */}
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
          <Title level={5} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
            <BugOutlined style={{ marginRight: '8px', color: '#ff4d4f' }} />
            异常检测结果
          </Title>
          <Badge count={anomalies.length} style={{ backgroundColor: '#ff4d4f' }} />
        </div>

        {/* 统计信息 */}
        <Space size="small" style={{ marginBottom: '12px' }}>
          <Badge count={levelStats.critical} style={{ backgroundColor: '#ff4d4f' }}>
            <Text style={{ fontSize: '12px' }}>严重</Text>
          </Badge>
          <Badge count={levelStats.warning} style={{ backgroundColor: '#faad14' }}>
            <Text style={{ fontSize: '12px' }}>警告</Text>
          </Badge>
          <Badge count={levelStats.info} style={{ backgroundColor: '#1890ff' }}>
            <Text style={{ fontSize: '12px' }}>提示</Text>
          </Badge>
        </Space>

        {/* 搜索和筛选 */}
        <Space direction="vertical" style={{ width: '100%' }} size="small">
          <Search
            placeholder="搜索异常内容..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
            size="small"
            allowClear
          />
          
          <Space size="small" style={{ width: '100%' }}>
            <Select
              value={levelFilter}
              onChange={setLevelFilter}
              size="small"
              style={{ width: '80px' }}
            >
              <Option value="all">全部级别</Option>
              <Option value="critical">严重</Option>
              <Option value="warning">警告</Option>
              <Option value="info">提示</Option>
            </Select>
            
            <Select
              value={typeFilter}
              onChange={setTypeFilter}
              size="small"
              style={{ flex: 1 }}
            >
              <Option value="all">全部类型</Option>
              {anomalyTypes.map(type => (
                <Option key={type} value={type}>
                  {anomalyTypeMap[type as keyof typeof anomalyTypeMap] || type}
                </Option>
              ))}
            </Select>
          </Space>
        </Space>
      </div>

      {/* 异常列表 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '8px' }}>
        {filteredAnomalies.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              anomalies.length === 0 ? 
                "暂无异常检测结果" : 
                "没有符合条件的异常"
            }
            style={{ marginTop: '40px' }}
          />
        ) : (
          <>
            {filteredAnomalies.map((anomaly, index) => (
              <ListItem
                key={anomaly.id}
                id={anomaly.id}
                title={anomaly.title}
                description={anomaly.description}
                content={anomaly.suggestion}
                location={anomaly.location}
                confidence={anomaly.confidence}
                level={anomaly.level}
                category={anomalyTypeMap[anomaly.type as keyof typeof anomalyTypeMap] || anomaly.type}
                originalText={anomaly.original_text}
                onClick={handleAnomalyClick}
                onCopy={handleCopy}
              />
            ))}
            
            {/* 底部提示 */}
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                <ExclamationCircleOutlined style={{ marginRight: '4px' }} />
                共发现 {filteredAnomalies.length} 个异常，点击可定位到文档位置
              </Text>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AnomalyList;
