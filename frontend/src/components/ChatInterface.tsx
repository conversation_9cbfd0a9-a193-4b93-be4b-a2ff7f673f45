/**
 * AI助手聊天界面组件
 * 提供与AI助手的交互功能
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Space, 
  Typography, 
  Avatar, 
  Spin, 
  message,
  Tooltip,
  Dropdown,
  Menu
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  ClearOutlined,
  DownloadOutlined,
  QuestionCircleOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { chatService, ChatMessage } from '../services/chatService';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface ChatInterfaceProps {
  sessionId?: string;
  onMessageSent?: (message: string) => void;
  disabled?: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  sessionId,
  onMessageSent,
  disabled = false
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);

  // 快捷问题
  const quickQuestions = chatService.getQuickQuestions();

  // 初始化聊天会话
  useEffect(() => {
    if (sessionId) {
      if (!chatService.setCurrentSession(sessionId)) {
        chatService.createSession(sessionId);
      }
    } else {
      chatService.createSession();
    }
    
    loadMessages();
  }, [sessionId]);

  // 加载消息
  const loadMessages = () => {
    const session = chatService.getCurrentSession();
    if (session) {
      setMessages([...session.messages]);
    }
  };

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || disabled) return;

    const messageContent = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    try {
      await chatService.sendMessage(messageContent, {
        sessionId,
        messageType: 'question'
      });
      
      loadMessages();
      
      if (onMessageSent) {
        onMessageSent(messageContent);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      message.error('发送消息失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 选择快捷问题
  const handleQuickQuestion = (question: string) => {
    setInputValue(question);
    inputRef.current?.focus();
  };

  // 清空聊天记录
  const handleClearChat = () => {
    chatService.clearCurrentSession();
    setMessages([]);
    message.success('聊天记录已清空');
  };

  // 导出聊天记录
  const handleExportChat = () => {
    const content = chatService.exportChatHistory();
    if (content) {
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `chat_history_${Date.now()}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('聊天记录已导出');
    }
  };

  // 渲染消息
  const renderMessage = (msg: ChatMessage) => {
    const isUser = msg.type === 'user';
    const isSystem = msg.type === 'system';
    
    return (
      <div
        key={msg.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: '12px'
        }}
      >
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          maxWidth: '85%',
          flexDirection: isUser ? 'row-reverse' : 'row'
        }}>
          {/* 头像 */}
          <Avatar
            size="small"
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
              margin: isUser ? '0 0 0 8px' : '0 8px 0 0'
            }}
          />
          
          {/* 消息内容 */}
          <div
            style={{
              background: isUser ? '#1890ff' : isSystem ? '#f0f0f0' : '#f6f6f6',
              color: isUser ? '#fff' : '#333',
              padding: '8px 12px',
              borderRadius: '8px',
              fontSize: '14px',
              lineHeight: '1.4',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word'
            }}
          >
            {msg.content}
            
            {/* 发送状态 */}
            {msg.status === 'sending' && (
              <div style={{ marginTop: '4px', textAlign: 'center' }}>
                <Spin size="small" />
              </div>
            )}
            
            {msg.status === 'error' && (
              <div style={{ marginTop: '4px', color: '#ff4d4f', fontSize: '12px' }}>
                发送失败
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 操作菜单
  const actionMenu = (
    <Menu>
      <Menu.Item key="clear" icon={<ClearOutlined />} onClick={handleClearChat}>
        清空聊天记录
      </Menu.Item>
      <Menu.Item key="export" icon={<DownloadOutlined />} onClick={handleExportChat}>
        导出聊天记录
      </Menu.Item>
    </Menu>
  );

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 标题栏 */}
      <div style={{ 
        padding: '16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Title level={5} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
          <RobotOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
          AI智能助手
        </Title>
        
        <Dropdown overlay={actionMenu} trigger={['click']}>
          <Button type="text" size="small" icon={<MoreOutlined />} />
        </Dropdown>
      </div>

      {/* 快捷问题 */}
      <div style={{ padding: '8px 16px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ marginBottom: '8px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <QuestionCircleOutlined style={{ marginRight: '4px' }} />
            快捷问题
          </Text>
        </div>
        <Space wrap size="small">
          {quickQuestions.slice(0, 4).map((question, index) => (
            <Button
              key={index}
              size="small"
              type="dashed"
              onClick={() => handleQuickQuestion(question)}
              disabled={disabled || isLoading}
              style={{ fontSize: '11px', height: '24px' }}
            >
              {question}
            </Button>
          ))}
        </Space>
      </div>

      {/* 消息列表 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '16px',
        background: '#fafafa'
      }}>
        {messages.length === 0 ? (
          <div style={{
            textAlign: 'center',
            marginTop: '40px',
            color: '#999'
          }}>
            <RobotOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
            <div>您好！我是AI智能助手</div>
            <div style={{ fontSize: '12px', marginTop: '4px' }}>
              我可以帮助您分析文档、解答问题、提供建议
            </div>
          </div>
        ) : (
          <>
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* 输入区域 */}
      <div style={{ 
        padding: '16px', 
        borderTop: '1px solid #f0f0f0',
        background: '#fff'
      }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的问题..."
            autoSize={{ minRows: 1, maxRows: 3 }}
            disabled={disabled || isLoading}
            style={{ resize: 'none' }}
          />
          <Tooltip title="发送 (Enter)">
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={isLoading}
              disabled={disabled || !inputValue.trim()}
              style={{ height: 'auto' }}
            />
          </Tooltip>
        </Space.Compact>
        
        <Text type="secondary" style={{ fontSize: '11px', marginTop: '4px', display: 'block' }}>
          按 Enter 发送，Shift + Enter 换行
        </Text>
      </div>
    </div>
  );
};

export default ChatInterface;
