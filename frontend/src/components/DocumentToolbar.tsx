/**
 * 文档工具栏组件
 * 提供缩放、搜索、导出等功能
 */

import React, { useState, useCallback } from 'react';
import { 
  Button, 
  Input, 
  Space, 
  Tooltip, 
  Slider, 
  Typography, 
  Divider,
  Badge
} from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  PrinterOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ClearOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Text } = Typography;

interface DocumentToolbarProps {
  zoom: number;
  onZoomChange: (zoom: number) => void;
  onSearch: (searchText: string) => void;
  onClearSearch: () => void;
  onReprocess: () => void;
  onExport: () => void;
  onPrint: () => void;
  searchResults?: {
    text: string;
    totalMatches: number;
    currentIndex: number;
  };
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  disabled?: boolean;
}

const DocumentToolbar: React.FC<DocumentToolbarProps> = ({
  zoom,
  onZoomChange,
  onSearch,
  onClearSearch,
  onReprocess,
  onExport,
  onPrint,
  searchResults,
  isFullscreen = false,
  onToggleFullscreen,
  disabled = false
}) => {
  const [searchText, setSearchText] = useState('');

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
    onSearch(value);
  }, [onSearch]);

  // 清除搜索
  const handleClearSearch = useCallback(() => {
    setSearchText('');
    onClearSearch();
  }, [onClearSearch]);

  // 缩放控制
  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(zoom + 0.1, 2.0);
    onZoomChange(newZoom);
  }, [zoom, onZoomChange]);

  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(zoom - 0.1, 0.5);
    onZoomChange(newZoom);
  }, [zoom, onZoomChange]);

  const handleZoomReset = useCallback(() => {
    onZoomChange(1.0);
  }, [onZoomChange]);

  const handleSliderChange = useCallback((value: number) => {
    onZoomChange(value / 100);
  }, [onZoomChange]);

  return (
    <div style={{
      padding: '8px 16px',
      background: '#fafafa',
      borderBottom: '1px solid #e8e8e8',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexWrap: 'wrap',
      gap: '8px'
    }}>
      {/* 左侧：缩放控制 */}
      <Space size="small">
        <Tooltip title="缩小">
          <Button
            icon={<ZoomOutOutlined />}
            size="small"
            onClick={handleZoomOut}
            disabled={disabled || zoom <= 0.5}
          />
        </Tooltip>
        
        <div style={{ width: '100px' }}>
          <Slider
            min={50}
            max={200}
            step={10}
            value={Math.round(zoom * 100)}
            onChange={handleSliderChange}
            disabled={disabled}
            tooltip={{ formatter: (value) => `${value}%` }}
          />
        </div>
        
        <Tooltip title="放大">
          <Button
            icon={<ZoomInOutlined />}
            size="small"
            onClick={handleZoomIn}
            disabled={disabled || zoom >= 2.0}
          />
        </Tooltip>
        
        <Button
          size="small"
          onClick={handleZoomReset}
          disabled={disabled}
        >
          <Text style={{ fontSize: '12px' }}>{Math.round(zoom * 100)}%</Text>
        </Button>
      </Space>

      {/* 中间：搜索功能 */}
      <Space size="small">
        <Search
          placeholder="搜索文档内容..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onSearch={handleSearch}
          style={{ width: '250px' }}
          size="small"
          disabled={disabled}
          suffix={
            searchResults && searchResults.totalMatches > 0 ? (
              <Badge 
                count={searchResults.totalMatches} 
                size="small"
                style={{ backgroundColor: '#52c41a' }}
              />
            ) : undefined
          }
        />
        
        {searchText && (
          <Tooltip title="清除搜索">
            <Button
              icon={<ClearOutlined />}
              size="small"
              onClick={handleClearSearch}
              disabled={disabled}
            />
          </Tooltip>
        )}
        
        {searchResults && searchResults.totalMatches > 0 && (
          <Text style={{ fontSize: '12px', color: '#666' }}>
            找到 {searchResults.totalMatches} 个匹配项
          </Text>
        )}
      </Space>

      {/* 右侧：操作按钮 */}
      <Space size="small">
        <Divider type="vertical" />
        
        <Tooltip title="重新处理">
          <Button
            icon={<ReloadOutlined />}
            size="small"
            onClick={onReprocess}
            disabled={disabled}
          />
        </Tooltip>
        
        <Tooltip title="导出文档">
          <Button
            icon={<DownloadOutlined />}
            size="small"
            onClick={onExport}
            disabled={disabled}
          />
        </Tooltip>
        
        <Tooltip title="打印文档">
          <Button
            icon={<PrinterOutlined />}
            size="small"
            onClick={onPrint}
            disabled={disabled}
          />
        </Tooltip>
        
        {onToggleFullscreen && (
          <Tooltip title={isFullscreen ? "退出全屏" : "全屏显示"}>
            <Button
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              size="small"
              onClick={onToggleFullscreen}
              disabled={disabled}
            />
          </Tooltip>
        )}
      </Space>
    </div>
  );
};

export default DocumentToolbar;
