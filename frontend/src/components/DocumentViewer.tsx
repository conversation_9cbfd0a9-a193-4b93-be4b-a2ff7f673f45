/**
 * 文档查看器组件
 * 支持HTML文档渲染、缩放、搜索和文本高亮功能
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { message, Spin } from 'antd';
import DocumentToolbar from './DocumentToolbar';
import { TextHighlighter, SearchResult } from '../utils/textHighlight';

interface DocumentViewerProps {
  htmlContent: string;
  loading?: boolean;
  anomalies?: Array<{
    id: string;
    original_text?: string;
    title: string;
    description: string;
    level?: string;
  }>;
  extractions?: Array<{
    id: string;
    original_text?: string;
    name: string;
    content?: string;
  }>;
  selectedItemId?: string | null;
  selectedItemType?: 'anomaly' | 'extraction' | null;
  onReprocess?: () => void;
  onExport?: () => void;
  onHighlightClick?: (type: 'anomaly' | 'extraction', id: string) => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  htmlContent,
  loading = false,
  anomalies = [],
  extractions = [],
  selectedItemId,
  selectedItemType,
  onReprocess,
  onExport,
  onHighlightClick
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const highlighterRef = useRef<TextHighlighter | null>(null);
  
  const [zoom, setZoom] = useState(1.0);
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 初始化高亮器
  useEffect(() => {
    if (contentRef.current && htmlContent) {
      highlighterRef.current = new TextHighlighter(contentRef.current);

      // 高亮异常和提取点
      if (anomalies.length > 0) {
        highlighterRef.current.highlightAnomalies(anomalies);
      }
      if (extractions.length > 0) {
        highlighterRef.current.highlightExtractions(extractions);
      }
    }

    return () => {
      if (highlighterRef.current) {
        highlighterRef.current.clearAllHighlights();
      }
    };
  }, [htmlContent, anomalies, extractions]);

  // 处理选中项的定位和高亮
  useEffect(() => {
    if (!selectedItemId || !selectedItemType || !highlighterRef.current) return;

    let targetText = '';
    let targetItem = null;

    if (selectedItemType === 'anomaly') {
      targetItem = anomalies.find(item => item.id === selectedItemId);
    } else if (selectedItemType === 'extraction') {
      targetItem = extractions.find(item => item.id === selectedItemId);
    }

    if (targetItem && targetItem.original_text) {
      targetText = targetItem.original_text;

      // 清除之前的搜索高亮
      highlighterRef.current.clearSearchHighlights();

      // 高亮目标文本
      const results = highlighterRef.current.searchAndHighlight(targetText);

      if (results.totalMatches > 0) {
        // 滚动到第一个匹配位置
        setTimeout(() => {
          if (highlighterRef.current && results.positions.length > 0) {
            highlighterRef.current.scrollToHighlight(results.positions[0].id);
          }
        }, 100);

        // 添加特殊的选中高亮样式
        setTimeout(() => {
          const elements = contentRef.current?.querySelectorAll('.search-highlight');
          elements?.forEach(el => {
            el.classList.add('selected-highlight');
            // 3秒后移除特殊样式
            setTimeout(() => {
              el.classList.remove('selected-highlight');
            }, 3000);
          });
        }, 200);
      }
    }
  }, [selectedItemId, selectedItemType, anomalies, extractions]);

  // 处理缩放变化
  const handleZoomChange = useCallback((newZoom: number) => {
    setZoom(newZoom);
  }, []);

  // 处理搜索
  const handleSearch = useCallback((searchText: string) => {
    if (!highlighterRef.current) return;

    if (!searchText.trim()) {
      handleClearSearch();
      return;
    }

    try {
      const results = highlighterRef.current.searchAndHighlight(searchText);
      setSearchResults(results);
      
      if (results.totalMatches > 0) {
        message.success(`找到 ${results.totalMatches} 个匹配项`);
        // 滚动到第一个匹配项
        if (results.positions.length > 0) {
          highlighterRef.current.scrollToHighlight(results.positions[0].id);
        }
      } else {
        message.info('未找到匹配的内容');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索功能出现错误');
    }
  }, []);

  // 清除搜索
  const handleClearSearch = useCallback(() => {
    if (highlighterRef.current) {
      highlighterRef.current.clearSearchHighlights();
    }
    setSearchResults(null);
  }, []);

  // 处理重新处理
  const handleReprocess = useCallback(() => {
    if (onReprocess) {
      onReprocess();
    }
  }, [onReprocess]);

  // 处理导出
  const handleExport = useCallback(() => {
    if (onExport) {
      onExport();
    } else {
      // 默认导出功能：下载HTML文件
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `document_${Date.now()}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('文档已导出');
    }
  }, [htmlContent, onExport]);

  // 处理打印
  const handlePrint = useCallback(() => {
    if (contentRef.current) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>打印文档</title>
              <style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; }
                .text-highlight { background-color: rgba(255, 255, 0, 0.4); }
                .highlight-anomaly { background-color: rgba(255, 0, 0, 0.2); }
                .highlight-extraction { background-color: rgba(0, 255, 0, 0.2); }
                @media print {
                  body { margin: 0; padding: 20px; }
                }
              </style>
            </head>
            <body>
              ${contentRef.current.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  }, []);

  // 处理全屏切换
  const handleToggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 处理高亮点击事件
  useEffect(() => {
    const handleHighlightClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const highlightId = target.getAttribute('data-highlight-id');
      
      if (highlightId && onHighlightClick) {
        if (target.classList.contains('highlight-anomaly')) {
          onHighlightClick('anomaly', highlightId);
        } else if (target.classList.contains('highlight-extraction')) {
          onHighlightClick('extraction', highlightId);
        }
      }
    };

    if (contentRef.current) {
      contentRef.current.addEventListener('click', handleHighlightClick);
    }

    return () => {
      if (contentRef.current) {
        contentRef.current.removeEventListener('click', handleHighlightClick);
      }
    };
  }, [onHighlightClick]);

  if (loading) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Spin size="large" tip="正在加载文档..." />
      </div>
    );
  }

  if (!htmlContent) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999'
      }}>
        暂无文档内容
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      style={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        background: '#fff'
      }}
    >
      {/* 工具栏 */}
      <DocumentToolbar
        zoom={zoom}
        onZoomChange={handleZoomChange}
        onSearch={handleSearch}
        onClearSearch={handleClearSearch}
        onReprocess={handleReprocess}
        onExport={handleExport}
        onPrint={handlePrint}
        searchResults={searchResults ? {
          text: searchResults.text,
          totalMatches: searchResults.totalMatches,
          currentIndex: 0
        } : undefined}
        isFullscreen={isFullscreen}
        onToggleFullscreen={handleToggleFullscreen}
        disabled={loading}
      />

      {/* 文档内容区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '20px',
        background: '#fff'
      }}>
        <div
          ref={contentRef}
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'top left',
            width: `${100 / zoom}%`,
            minHeight: `${100 / zoom}%`,
            transition: 'transform 0.2s ease'
          }}
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>
    </div>
  );
};

export default DocumentViewer;
