/**
 * 提取点列表组件
 * 显示智能体提取的107个数据点
 */

import React, { useState, useMemo } from 'react';
import { Typography, Input, Select, Space, Badge, Empty, Collapse, Progress } from 'antd';
import { SearchOutlined, DatabaseOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import ListItem from './ListItem';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;

interface ExtractionItem {
  id: string;
  name: string;
  category: string;
  content?: string;
  location: string;
  confidence?: number;
  original_text?: string;
  is_required?: boolean;
  data_type?: string;
}

interface ExtractionListProps {
  extractions: ExtractionItem[];
  loading?: boolean;
  onExtractionClick?: (extractionId: string) => void;
  onExtractionCopy?: (content: string) => void;
}

const ExtractionList: React.FC<ExtractionListProps> = ({
  extractions = [],
  loading = false,
  onExtractionClick,
  onExtractionCopy
}) => {
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeKey, setActiveKey] = useState<string[]>(['basic_info']);

  // 分类映射
  const categoryMap = {
    'basic_info': '基本信息',
    'bidder_requirements': '投标人要求',
    'technical_specs': '技术规格',
    'commercial_terms': '商务条款',
    'evaluation_criteria': '评标标准',
    'contract_terms': '合同条款',
    'other': '其他'
  };

  // 获取分类列表
  const categories = useMemo(() => {
    const cats = new Set(extractions.map(item => item.category));
    return Array.from(cats);
  }, [extractions]);

  // 过滤提取列表
  const filteredExtractions = useMemo(() => {
    return extractions.filter(extraction => {
      // 搜索过滤
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        const matchesSearch = 
          extraction.name.toLowerCase().includes(searchLower) ||
          (extraction.content && extraction.content.toLowerCase().includes(searchLower)) ||
          (extraction.original_text && extraction.original_text.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // 分类过滤
      if (categoryFilter !== 'all' && extraction.category !== categoryFilter) {
        return false;
      }

      // 状态过滤
      if (statusFilter === 'extracted' && !extraction.content) {
        return false;
      }
      if (statusFilter === 'missing' && extraction.content) {
        return false;
      }

      return true;
    });
  }, [extractions, searchText, categoryFilter, statusFilter]);

  // 按分类分组
  const groupedExtractions = useMemo(() => {
    const groups: Record<string, ExtractionItem[]> = {};
    
    filteredExtractions.forEach(extraction => {
      const category = extraction.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(extraction);
    });

    return groups;
  }, [filteredExtractions]);

  // 统计信息
  const stats = useMemo(() => {
    const total = extractions.length;
    const extracted = extractions.filter(item => item.content).length;
    const required = extractions.filter(item => item.is_required).length;
    const requiredExtracted = extractions.filter(item => item.is_required && item.content).length;
    
    return {
      total,
      extracted,
      missing: total - extracted,
      completionRate: total > 0 ? (extracted / total) * 100 : 0,
      required,
      requiredExtracted,
      requiredCompletionRate: required > 0 ? (requiredExtracted / required) * 100 : 0
    };
  }, [extractions]);

  // 处理提取点击
  const handleExtractionClick = (extractionId: string) => {
    if (onExtractionClick) {
      onExtractionClick(extractionId);
    }
  };

  // 处理复制
  const handleCopy = (content: string) => {
    if (onExtractionCopy) {
      onExtractionCopy(content);
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 标题和统计 */}
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
          <Title level={5} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
            <DatabaseOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
            数据提取结果
          </Title>
          <Badge count={stats.extracted} style={{ backgroundColor: '#52c41a' }} />
        </div>

        {/* 完成率 */}
        <div style={{ marginBottom: '12px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            <Text style={{ fontSize: '12px' }}>总体完成率</Text>
            <Text style={{ fontSize: '12px' }}>{stats.extracted}/{stats.total}</Text>
          </div>
          <Progress 
            percent={stats.completionRate} 
            size="small" 
            strokeColor="#52c41a"
            showInfo={false}
          />
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '4px', marginBottom: '4px' }}>
            <Text style={{ fontSize: '12px' }}>必填项完成率</Text>
            <Text style={{ fontSize: '12px' }}>{stats.requiredExtracted}/{stats.required}</Text>
          </div>
          <Progress 
            percent={stats.requiredCompletionRate} 
            size="small" 
            strokeColor="#faad14"
            showInfo={false}
          />
        </div>

        {/* 搜索和筛选 */}
        <Space direction="vertical" style={{ width: '100%' }} size="small">
          <Search
            placeholder="搜索提取点..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
            size="small"
            allowClear
          />
          
          <Space size="small" style={{ width: '100%' }}>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              size="small"
              style={{ width: '80px' }}
            >
              <Option value="all">全部</Option>
              <Option value="extracted">已提取</Option>
              <Option value="missing">未提取</Option>
            </Select>
            
            <Select
              value={categoryFilter}
              onChange={setCategoryFilter}
              size="small"
              style={{ flex: 1 }}
            >
              <Option value="all">全部分类</Option>
              {categories.map(category => (
                <Option key={category} value={category}>
                  {categoryMap[category as keyof typeof categoryMap] || category}
                </Option>
              ))}
            </Select>
          </Space>
        </Space>
      </div>

      {/* 提取列表 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '8px' }}>
        {Object.keys(groupedExtractions).length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              extractions.length === 0 ? 
                "暂无数据提取结果" : 
                "没有符合条件的提取点"
            }
            style={{ marginTop: '40px' }}
          />
        ) : (
          <Collapse
            activeKey={activeKey}
            onChange={(keys) => setActiveKey(Array.isArray(keys) ? keys : [keys])}
            size="small"
            ghost
          >
            {Object.entries(groupedExtractions).map(([category, items]) => {
              const categoryName = categoryMap[category as keyof typeof categoryMap] || category;
              const extractedCount = items.filter(item => item.content).length;
              
              return (
                <Panel
                  key={category}
                  header={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text strong>{categoryName}</Text>
                      <Space size="small">
                        <Badge 
                          count={extractedCount} 
                          style={{ backgroundColor: '#52c41a' }} 
                        />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {extractedCount}/{items.length}
                        </Text>
                      </Space>
                    </div>
                  }
                >
                  {items.map((extraction) => (
                    <ListItem
                      key={extraction.id}
                      id={extraction.id}
                      title={extraction.name}
                      description={extraction.content ? undefined : '未提取到内容'}
                      content={extraction.content}
                      location={extraction.location}
                      confidence={extraction.confidence}
                      level={extraction.content ? 'success' : 'warning'}
                      category={extraction.data_type}
                      originalText={extraction.original_text}
                      isRequired={extraction.is_required}
                      onClick={handleExtractionClick}
                      onCopy={handleCopy}
                      style={{
                        marginBottom: '4px',
                        borderLeft: extraction.content ? 
                          '4px solid #52c41a' : 
                          '4px solid #faad14'
                      }}
                    />
                  ))}
                </Panel>
              );
            })}
          </Collapse>
        )}
        
        {/* 底部提示 */}
        {Object.keys(groupedExtractions).length > 0 && (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <CheckCircleOutlined style={{ marginRight: '4px', color: '#52c41a' }} />
              已提取 {stats.extracted} 个数据点
              <CloseCircleOutlined style={{ marginLeft: '8px', marginRight: '4px', color: '#faad14' }} />
              缺失 {stats.missing} 个数据点
            </Text>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExtractionList;
