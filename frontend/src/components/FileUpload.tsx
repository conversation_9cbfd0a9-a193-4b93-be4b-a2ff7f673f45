/**
 * 文件上传组件
 * 支持拖拽上传和点击上传Word文档
 */

import React, { useState } from 'react';
import { Upload, message, Progress, Typography, Space, Button } from 'antd';
import { InboxOutlined, FileTextOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { apiService } from '../services/api';
import { FileUploadResponse } from '../types';

const { Dragger } = Upload;
const { Text, Title } = Typography;

interface FileUploadComponentProps {
  onUploadSuccess?: (response: FileUploadResponse) => void;
  onUploadError?: (error: string) => void;
  disabled?: boolean;
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  onUploadSuccess,
  onUploadError,
  disabled = false
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<FileUploadResponse | null>(null);

  // 文件上传前的验证
  const beforeUpload = (file: File) => {
    // 检查文件类型
    const isWordDoc = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                      file.type === 'application/msword' ||
                      file.name.toLowerCase().endsWith('.doc') ||
                      file.name.toLowerCase().endsWith('.docx');
    
    if (!isWordDoc) {
      message.error('只能上传Word文档文件(.doc/.docx)！');
      return false;
    }

    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error('文件大小不能超过50MB！');
      return false;
    }

    return true;
  };

  // 自定义上传处理
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    try {
      setUploading(true);
      setUploadProgress(0);

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      // 调用API上传文件
      const response = await apiService.uploadFile(file);

      // 清除进度定时器
      clearInterval(progressInterval);
      setUploadProgress(100);

      // 保存上传结果
      setUploadedFile(response);

      // 调用成功回调
      onSuccess(response);
      onUploadSuccess?.(response);

      message.success(`文件 ${file.name} 上传成功！`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      
      onError(error);
      onUploadError?.(errorMessage);
      
      message.error(`上传失败: ${errorMessage}`);
    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  };

  // 删除已上传的文件
  const handleRemoveFile = async () => {
    if (!uploadedFile) return;

    try {
      await apiService.cleanupSession(uploadedFile.session_id);
      setUploadedFile(null);
      message.success('文件删除成功');
    } catch (error) {
      message.error('删除文件失败');
    }
  };

  // 上传组件属性
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    disabled: disabled || uploading,
    beforeUpload,
    customRequest: customUpload,
    showUploadList: false,
    accept: '.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  };

  return (
    <div style={{ width: '100%' }}>
      <Title level={5} style={{ marginBottom: 8 }}>
        <FileTextOutlined style={{ marginRight: 4 }} />
        文件上传
      </Title>

      {!uploadedFile ? (
        <div>
          <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ color: uploading ? '#1890ff' : '#d9d9d9' }} />
            </p>
            <p className="ant-upload-text">
              {uploading ? '正在上传...' : '点击或拖拽文件到此区域上传'}
            </p>
            <p className="ant-upload-hint">
              支持Word文档格式(.doc/.docx)，文件大小限制50MB
            </p>
          </Dragger>

          {uploading && (
            <Progress 
              percent={uploadProgress} 
              status={uploadProgress === 100 ? 'success' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          )}
        </div>
      ) : (
        <div style={{
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '16px',
          background: '#f6ffed'
        }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                <FileTextOutlined style={{ color: '#52c41a' }} />
                <Text strong>{uploadedFile.filename}</Text>
              </Space>
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                onClick={handleRemoveFile}
                size="small"
                danger
              />
            </div>
            
            <div>
              <Text type="secondary">
                文件大小: {(uploadedFile.file_size / 1024 / 1024).toFixed(2)} MB
              </Text>
              <br />
              <Text type="secondary">
                上传时间: {new Date(uploadedFile.upload_time).toLocaleString()}
              </Text>
              <br />
              <Text type="secondary">
                会话ID: {uploadedFile.session_id}
              </Text>
            </div>
          </Space>
        </div>
      )}
    </div>
  );
};

export default FileUploadComponent;
