/**
 * 三栏布局组件
 * 实现左侧面板、中间文档区域、右侧面板的布局
 */

import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Layout as AntdLayout, Typography, Divider, message, Button, Progress, Spin } from 'antd';
import { FileTextOutlined, BugOutlined, RobotOutlined, PlayCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import FileUploadComponent from './FileUpload';
import DocumentViewer from './DocumentViewer';
import AnomalyList from './AnomalyList';
import ExtractionList from './ExtractionList';
import ChatInterface from './ChatInterface';
import ProcessStatusComponent from './ProcessStatus';
import { FileUploadResponse, ProcessStatus } from '../types';
import { apiService } from '../services/api';

const { Header, Content } = AntdLayout;
const { Title, Text } = Typography;

const Layout: React.FC = () => {
  const [currentSession, setCurrentSession] = useState<FileUploadResponse | null>(null);
  const [processing, setProcessing] = useState(false);
  const [processStatus, setProcessStatus] = useState<ProcessStatus | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [documentHtml, setDocumentHtml] = useState('');
  const [documentStats, setDocumentStats] = useState<any>(null);
  const [anomalies, setAnomalies] = useState<any[]>([]);
  const [extractions, setExtractions] = useState<any[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [selectedItemType, setSelectedItemType] = useState<'anomaly' | 'extraction' | null>(null);

  // 演示模式数据
  const loadDemoData = () => {
    const demoHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body {
              font-family: 'Microsoft YaHei', Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
            }
            h1, h2, h3 { color: #2c3e50; margin: 20px 0 10px 0; }
            p { margin: 10px 0; }
            table { border-collapse: collapse; width: 100%; margin: 15px 0; }
            td, th { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <h1>某某工程建设项目招标文件</h1>
          <p><strong>招标编号：</strong>ZB2025-001</p>
          <p><strong>招标人：</strong>某某建设集团有限公司</p>
          <p><strong>项目名称：</strong>某某工程建设项目</p>

          <h2>一、项目概述</h2>
          <p>本项目为某某工程建设项目，总投资预算约<span style="color: red;">5000万元人民币</span>。项目建设期为24个月，要求投标人具备相应的资质和业绩。</p>

          <h2>二、投标人资格要求</h2>
          <p>1. 投标人注册资本不少于<span style="color: green;">1000万元人民币</span></p>
          <p>2. 具有建筑工程施工总承包一级资质</p>
          <p>3. 近三年内完成过类似工程项目不少于2个</p>

          <h2>三、技术要求</h2>
          <p>本工程采用框架结构，抗震设防烈度为7度，建筑高度不超过50米。</p>

          <h2>四、商务条款</h2>
          <table>
            <tr>
              <th>项目</th>
              <th>要求</th>
            </tr>
            <tr>
              <td>付款方式</td>
              <td><span style="color: green;">按工程进度分期付款</span></td>
            </tr>
            <tr>
              <td>质保期</td>
              <td>2年</td>
            </tr>
            <tr>
              <td>工期</td>
              <td>24个月</td>
            </tr>
          </table>

          <h2>五、投标文件要求</h2>
          <p>投标文件应包括商务标和技术标两部分，技术标占60%，商务标占40%。</p>

          <p><strong>投标截止时间：</strong>2025年8月15日 14:00</p>
          <p><strong>开标时间：</strong>2025年8月15日 14:30</p>
          <p><strong>开标地点：</strong>某某市公共资源交易中心</p>
        </body>
      </html>
    `;

    const demoAnomalies = [
      {
        id: "anomaly_001",
        type: "missing_info",
        level: "warning",
        title: "缺少项目预算信息",
        description: "招标文件中未明确说明项目预算金额",
        location: "第2页，项目概述部分",
        suggestion: "建议补充详细的项目预算说明",
        original_text: "5000万元人民币",
        confidence: 0.85
      },
      {
        id: "anomaly_002",
        type: "format_error",
        level: "info",
        title: "日期格式不统一",
        description: "文档中日期格式存在不一致",
        location: "第3页，时间安排部分",
        suggestion: "建议统一使用YYYY-MM-DD格式",
        original_text: "2025年8月15日",
        confidence: 0.92
      }
    ];

    const demoExtractions = [
      {
        id: "001",
        name: "项目名称",
        category: "basic_info",
        content: "某某工程建设项目",
        location: "第1页，标题部分",
        confidence: 0.95,
        original_text: "某某工程建设项目",
        is_required: true,
        data_type: "text"
      },
      {
        id: "002",
        name: "招标编号",
        category: "basic_info",
        content: "ZB2025-001",
        location: "第1页，编号部分",
        confidence: 0.98,
        original_text: "ZB2025-001",
        is_required: true,
        data_type: "text"
      },
      {
        id: "003",
        name: "招标人",
        category: "basic_info",
        content: "某某建设集团有限公司",
        location: "第1页，招标人信息",
        confidence: 0.90,
        original_text: "某某建设集团有限公司",
        is_required: true,
        data_type: "text"
      },
      {
        id: "021",
        name: "注册资本要求",
        category: "bidder_requirements",
        content: "不少于1000万元人民币",
        location: "第5页，投标人资格要求",
        confidence: 0.88,
        original_text: "1000万元人民币",
        is_required: true,
        data_type: "text"
      },
      {
        id: "071",
        name: "付款方式",
        category: "commercial_terms",
        content: "按进度分期付款",
        location: "第8页，商务条款",
        confidence: 0.82,
        original_text: "按工程进度分期付款",
        is_required: true,
        data_type: "text"
      }
    ];

    const demoStats = {
      total_words: 256,
      total_paragraphs: 12,
      estimated_pages: 3,
      total_characters: 1024,
      total_tables: 1
    };

    // 设置演示数据
    setDocumentHtml(demoHtml);
    setAnomalies(demoAnomalies);
    setExtractions(demoExtractions);
    setDocumentStats(demoStats);
    setProcessStatus(ProcessStatus.COMPLETED);
    setProgress(100);
    setCurrentStep('演示数据加载完成');

    // 模拟会话信息
    setCurrentSession({
      success: true,
      message: "演示文件上传成功",
      session_id: "demo-session-12345",
      filename: "演示招标文件.docx",
      file_size: 51200,
      upload_time: new Date().toISOString(),
      timestamp: new Date().toISOString()
    });

    message.success('演示数据已加载，您可以体验所有功能！');
  };

  // 处理异常点击
  const handleAnomalyClick = (anomalyId: string) => {
    console.log('点击异常:', anomalyId);
    setSelectedItemId(anomalyId);
    setSelectedItemType('anomaly');

    // 查找对应的异常数据
    const anomaly = anomalies.find(item => item.id === anomalyId);
    if (anomaly && anomaly.original_text) {
      // 触发文档查看器的高亮和定位
      // 这里会通过DocumentViewer的highlightAndScroll功能实现
      message.success(`已定位到异常: ${anomaly.title}`);
    } else {
      message.info(`定位异常: ${anomalyId}`);
    }
  };

  // 处理提取点击
  const handleExtractionClick = (extractionId: string) => {
    console.log('点击提取点:', extractionId);
    setSelectedItemId(extractionId);
    setSelectedItemType('extraction');

    // 查找对应的提取数据
    const extraction = extractions.find(item => item.id === extractionId);
    if (extraction && extraction.original_text) {
      // 触发文档查看器的高亮和定位
      message.success(`已定位到提取点: ${extraction.name}`);
    } else {
      message.info(`定位提取点: ${extractionId}`);
    }
  };

  // 处理复制
  const handleCopy = (content: string) => {
    console.log('复制内容:', content);
  };

  // 处理文件上传成功
  const handleUploadSuccess = (response: FileUploadResponse) => {
    setCurrentSession(response);
    setProcessStatus(null);
    setProgress(0);
    setDocumentHtml('');
    setDocumentStats(null);
    setAnomalies([]);
    setExtractions([]);
    message.success('文件上传成功，可以开始处理文档');
  };

  // 处理文件上传失败
  const handleUploadError = (error: string) => {
    message.error(`上传失败: ${error}`);
  };

  // 开始处理文档
  const handleStartProcessing = async () => {
    if (!currentSession) {
      message.error('请先上传文档');
      return;
    }

    try {
      setProcessing(true);
      setProgress(0);
      setCurrentStep('启动文档解析...');

      // 调用解析API
      const response = await apiService.processDocument(currentSession.session_id);

      if (response.success) {
        message.success('文档解析已启动');
        setProcessStatus(ProcessStatus.PARSING);

        // 开始轮询状态
        pollProcessStatus();
      } else {
        throw new Error(response.message || '启动解析失败');
      }
    } catch (error) {
      message.error(`启动解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setProcessing(false);
    }
  };

  // 轮询处理状态
  const pollProcessStatus = async () => {
    if (!currentSession) return;

    try {
      const statusResponse = await apiService.getProcessStatus(currentSession.session_id);

      setProcessStatus(statusResponse.status);
      setProgress(statusResponse.progress);
      setCurrentStep(statusResponse.current_step);

      if (statusResponse.status === ProcessStatus.COMPLETED) {
        // 处理完成，获取结果
        await fetchProcessResults();
        setProcessing(false);
        message.success('文档处理完成');
      } else if (statusResponse.status === ProcessStatus.ERROR) {
        // 处理失败
        setProcessing(false);
        message.error('文档处理失败');
      } else {
        // 继续轮询
        setTimeout(pollProcessStatus, 2000);
      }
    } catch (error) {
      console.error('查询状态失败:', error);
      setTimeout(pollProcessStatus, 3000); // 出错时延长轮询间隔
    }
  };

  // 获取处理结果
  const fetchProcessResults = async () => {
    if (!currentSession) return;

    try {
      const results = await apiService.getProcessResults(currentSession.session_id);

      if (results.success) {
        setDocumentHtml(results.document_html);
        setDocumentStats(results.statistics);
        setAnomalies(results.anomalies || []);
        setExtractions(results.extractions || []);
      }
    } catch (error) {
      console.error('获取结果失败:', error);
      message.error('获取处理结果失败');
    }
  };

  return (
    <AntdLayout className="main-layout">
      {/* 顶部标题栏 */}
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        borderBottom: '1px solid #e8e8e8',
        height: '64px',
        lineHeight: '64px'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          <FileTextOutlined style={{ marginRight: 8 }} />
          招标文件智能审查与提取系统
        </Title>
      </Header>

      <Content style={{ height: 'calc(100vh - 64px)' }}>
        <div className="main-layout">
          {/* 左侧面板 */}
          <div className="left-panel">
            {/* 文件上传区域 */}
            <div style={{
              minHeight: '180px',
              padding: '16px',
              borderBottom: '1px solid #e8e8e8'
            }}>
              <FileUploadComponent
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
              />
            </div>

            {/* 异常点区域 */}
            <div style={{
              flex: '0 0 40%',
              borderBottom: '1px solid #e8e8e8',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <AnomalyList
                anomalies={anomalies}
                loading={processing}
                onAnomalyClick={handleAnomalyClick}
                onAnomalyCopy={handleCopy}
              />
            </div>

            {/* 提取点区域 */}
            <div style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <ExtractionList
                extractions={extractions}
                loading={processing}
                onExtractionClick={handleExtractionClick}
                onExtractionCopy={handleCopy}
              />
            </div>
          </div>

          {/* 中间文档区域 */}
          <div className="center-panel">
            {/* 工具栏 */}
            <div style={{
              height: '50px',
              padding: '8px 16px',
              borderBottom: '1px solid #e8e8e8',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              background: '#fafafa'
            }}>
              <div>
                <Text strong>文档查看器</Text>
              </div>
              <div>
                <Text type="secondary">缩放: 100%</Text>
              </div>
            </div>

            {/* 文档展示区域 */}
            <div className="panel-content" style={{
              background: '#fff',
              padding: '0'
            }}>
              <DocumentViewer
                htmlContent={documentHtml}
                loading={processing}
                anomalies={anomalies}
                extractions={extractions}
                selectedItemId={selectedItemId}
                selectedItemType={selectedItemType}
                onReprocess={handleStartProcessing}
                onHighlightClick={(type, id) => {
                  console.log(`点击了${type}: ${id}`);
                  if (type === 'anomaly') {
                    handleAnomalyClick(id);
                  } else if (type === 'extraction') {
                    handleExtractionClick(id);
                  }
                }}
              />

              {!documentHtml && !processing && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center'
                }}>
                  <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
                  <br />
                  <Text type="secondary">
                    {currentSession ?
                      `已上传文档: ${currentSession.filename}` :
                      '请上传Word文档开始处理'
                    }
                  </Text>
                  {currentSession && (
                    <div style={{ marginTop: '16px' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        会话ID: {currentSession.session_id}
                      </Text>
                    </div>
                  )}

                  {/* 演示按钮 */}
                  {!currentSession && (
                    <div style={{ marginTop: '24px' }}>
                      <Button
                        type="primary"
                        onClick={loadDemoData}
                        style={{ marginRight: '8px' }}
                      >
                        🎯 加载演示数据
                      </Button>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px', display: 'block' }}>
                        体验完整的文档查看器功能
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 右侧面板 */}
          <div className="right-panel">
            {/* 处理状态区域 */}
            <div style={{ padding: '16px 16px 0' }}>
              <ProcessStatusComponent
                status={processStatus}
                progress={progress}
                currentStep={currentStep}
                fileName={currentSession?.filename}
                onRetry={handleStartProcessing}
                error={processStatus === ProcessStatus.ERROR ? '处理过程中出现错误' : undefined}
              />

              {/* 开始处理按钮 */}
              {!processing && processStatus !== ProcessStatus.COMPLETED && currentSession && (
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleStartProcessing}
                  block
                  style={{ marginBottom: '16px' }}
                >
                  开始处理文档
                </Button>
              )}

              {/* 重新处理按钮 */}
              {processStatus === ProcessStatus.COMPLETED && (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleStartProcessing}
                  block
                  style={{ marginBottom: '16px' }}
                >
                  重新处理
                </Button>
              )}
            </div>

            {/* AI助手交互界面 */}
            <div style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <ChatInterface
                sessionId={currentSession?.session_id}
                onMessageSent={(message) => {
                  console.log('用户发送消息:', message);
                }}
                disabled={processing}
              />
            </div>
          </div>
        </div>

        {/* 路由出口 */}
        <div style={{ display: 'none' }}>
          <Outlet />
        </div>
      </Content>
    </AntdLayout>
  );
};

export default Layout;
