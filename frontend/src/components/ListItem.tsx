/**
 * 通用列表项组件
 * 提供统一的列表项样式和交互功能
 */

import React, { useState } from 'react';
import { Card, Typography, Tag, Button, Space, Tooltip, message } from 'antd';
import { 
  EnvironmentOutlined, 
  CopyOutlined, 
  EyeOutlined,
  DownOutlined,
  RightOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface ListItemProps {
  id: string;
  title: string;
  description?: string;
  content?: string;
  location?: string;
  confidence?: number;
  level?: 'critical' | 'warning' | 'info' | 'success';
  category?: string;
  originalText?: string;
  isRequired?: boolean;
  onClick?: (id: string) => void;
  onCopy?: (content: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

const ListItem: React.FC<ListItemProps> = ({
  id,
  title,
  description,
  content,
  location,
  confidence,
  level = 'info',
  category,
  originalText,
  isRequired = false,
  onClick,
  onCopy,
  className,
  style
}) => {
  const [expanded, setExpanded] = useState(false);
  const [hovered, setHovered] = useState(false);

  // 获取级别对应的颜色
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return '#ff4d4f';
      case 'warning': return '#faad14';
      case 'info': return '#1890ff';
      case 'success': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  // 获取级别对应的标签
  const getLevelTag = (level: string) => {
    switch (level) {
      case 'critical': return { color: 'red', text: '严重' };
      case 'warning': return { color: 'orange', text: '警告' };
      case 'info': return { color: 'blue', text: '提示' };
      case 'success': return { color: 'green', text: '正常' };
      default: return { color: 'default', text: '未知' };
    }
  };

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick(id);
    }
  };

  // 处理复制事件
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
      if (onCopy) {
        onCopy(text);
      }
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 切换展开状态
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const levelTag = getLevelTag(level);

  return (
    <Card
      size="small"
      className={className}
      style={{
        marginBottom: '8px',
        cursor: 'pointer',
        borderLeft: `4px solid ${getLevelColor(level)}`,
        transition: 'all 0.3s ease',
        transform: hovered ? 'translateX(2px)' : 'translateX(0)',
        boxShadow: hovered ? '0 2px 8px rgba(0,0,0,0.1)' : '0 1px 3px rgba(0,0,0,0.05)',
        ...style
      }}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onClick={handleClick}
      bodyStyle={{ padding: '12px' }}
    >
      {/* 头部信息 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1, minWidth: 0 }}>
          {/* 标题和标签 */}
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <Text strong style={{ marginRight: '8px', fontSize: '14px' }}>
              {title}
            </Text>
            <Tag color={levelTag.color} size="small">
              {levelTag.text}
            </Tag>
            {isRequired && (
              <Tag color="red" size="small">必填</Tag>
            )}
            {category && (
              <Tag color="blue" size="small">{category}</Tag>
            )}
          </div>

          {/* 内容预览 */}
          {content && (
            <Paragraph 
              style={{ 
                margin: '4px 0',
                fontSize: '13px',
                color: '#666'
              }}
              ellipsis={{ rows: 1, expandable: false }}
            >
              {content}
            </Paragraph>
          )}

          {/* 描述 */}
          {description && (
            <Text 
              type="secondary" 
              style={{ 
                fontSize: '12px',
                display: expanded ? 'block' : '-webkit-box',
                WebkitLineClamp: expanded ? 'none' : 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}
            >
              {description}
            </Text>
          )}
        </div>

        {/* 操作按钮 */}
        <Space size="small">
          {confidence && (
            <Tooltip title={`置信度: ${Math.round(confidence * 100)}%`}>
              <Tag color={confidence > 0.8 ? 'green' : confidence > 0.6 ? 'orange' : 'red'}>
                {Math.round(confidence * 100)}%
              </Tag>
            </Tooltip>
          )}
          
          {(description || originalText) && (
            <Button
              type="text"
              size="small"
              icon={expanded ? <DownOutlined /> : <RightOutlined />}
              onClick={toggleExpanded}
            />
          )}
        </Space>
      </div>

      {/* 展开的详细信息 */}
      {expanded && (
        <div style={{ marginTop: '12px', paddingTop: '12px', borderTop: '1px solid #f0f0f0' }}>
          {/* 位置信息 */}
          {location && (
            <div style={{ marginBottom: '8px' }}>
              <EnvironmentOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                位置: {location}
              </Text>
            </div>
          )}

          {/* 原文内容 */}
          {originalText && (
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: '4px' }}>
                原文:
              </Text>
              <div style={{
                background: '#f6f6f6',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px',
                position: 'relative'
              }}>
                <Text style={{ fontSize: '12px' }}>{originalText}</Text>
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  style={{ position: 'absolute', top: '4px', right: '4px' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopy(originalText);
                  }}
                />
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <Space size="small">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleClick();
              }}
            >
              定位
            </Button>
            
            {content && (
              <Button
                size="small"
                icon={<CopyOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopy(content);
                }}
              >
                复制内容
              </Button>
            )}
          </Space>
        </div>
      )}
    </Card>
  );
};

export default ListItem;
