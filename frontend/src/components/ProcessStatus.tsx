/**
 * 处理状态组件
 * 显示当前处理状态、进度和错误信息
 */

import React from 'react';
import { Card, Progress, Typography, Space, Tag, Alert, Spin } from 'antd';
import { 
  LoadingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

export enum ProcessStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  PARSING = 'parsing',
  ANALYZING = 'analyzing',
  EXTRACTING = 'extracting',
  COMPLETED = 'completed',
  ERROR = 'error'
}

interface ProcessStatusProps {
  status: ProcessStatus;
  progress: number;
  currentStep?: string;
  estimatedTime?: number;
  error?: string;
  fileName?: string;
  onRetry?: () => void;
}

const ProcessStatusComponent: React.FC<ProcessStatusProps> = ({
  status,
  progress,
  currentStep,
  estimatedTime,
  error,
  fileName,
  onRetry
}) => {
  // 获取状态配置
  const getStatusConfig = (status: ProcessStatus) => {
    switch (status) {
      case ProcessStatus.IDLE:
        return {
          icon: <FileTextOutlined style={{ color: '#d9d9d9' }} />,
          color: 'default',
          text: '等待处理',
          description: '请上传文档开始处理'
        };
      case ProcessStatus.UPLOADING:
        return {
          icon: <LoadingOutlined style={{ color: '#1890ff' }} />,
          color: 'processing',
          text: '上传中',
          description: '正在上传文档文件'
        };
      case ProcessStatus.PARSING:
        return {
          icon: <LoadingOutlined style={{ color: '#1890ff' }} />,
          color: 'processing',
          text: '解析中',
          description: '正在解析文档内容'
        };
      case ProcessStatus.ANALYZING:
        return {
          icon: <LoadingOutlined style={{ color: '#1890ff' }} />,
          color: 'processing',
          text: '智能审查中',
          description: 'AI正在分析文档异常'
        };
      case ProcessStatus.EXTRACTING:
        return {
          icon: <LoadingOutlined style={{ color: '#1890ff' }} />,
          color: 'processing',
          text: '数据提取中',
          description: 'AI正在提取关键数据'
        };
      case ProcessStatus.COMPLETED:
        return {
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
          color: 'success',
          text: '处理完成',
          description: '文档处理已完成'
        };
      case ProcessStatus.ERROR:
        return {
          icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
          color: 'error',
          text: '处理失败',
          description: '文档处理过程中出现错误'
        };
      default:
        return {
          icon: <ClockCircleOutlined style={{ color: '#d9d9d9' }} />,
          color: 'default',
          text: '未知状态',
          description: ''
        };
    }
  };

  const statusConfig = getStatusConfig(status);
  const isProcessing = [
    ProcessStatus.UPLOADING,
    ProcessStatus.PARSING,
    ProcessStatus.ANALYZING,
    ProcessStatus.EXTRACTING
  ].includes(status);

  // 格式化剩余时间
  const formatEstimatedTime = (seconds: number): string => {
    if (seconds < 60) {
      return `约 ${Math.ceil(seconds)} 秒`;
    } else if (seconds < 3600) {
      return `约 ${Math.ceil(seconds / 60)} 分钟`;
    } else {
      return `约 ${Math.ceil(seconds / 3600)} 小时`;
    }
  };

  return (
    <Card 
      size="small" 
      style={{ marginBottom: '16px' }}
      bodyStyle={{ padding: '12px' }}
    >
      {/* 状态标题 */}
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
        {statusConfig.icon}
        <Title level={5} style={{ margin: '0 0 0 8px', flex: 1 }}>
          处理状态
        </Title>
        <Tag color={statusConfig.color as any}>
          {statusConfig.text}
        </Tag>
      </div>

      {/* 文件信息 */}
      {fileName && (
        <div style={{ marginBottom: '8px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            文件: {fileName}
          </Text>
        </div>
      )}

      {/* 进度条 */}
      {isProcessing && (
        <div style={{ marginBottom: '8px' }}>
          <Progress
            percent={progress}
            size="small"
            status={status === ProcessStatus.ERROR ? 'exception' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>
      )}

      {/* 当前步骤 */}
      {currentStep && (
        <div style={{ marginBottom: '8px' }}>
          <Space size="small">
            {isProcessing && <Spin size="small" />}
            <Text style={{ fontSize: '12px' }}>
              {currentStep}
            </Text>
          </Space>
        </div>
      )}

      {/* 预计时间 */}
      {estimatedTime && estimatedTime > 0 && isProcessing && (
        <div style={{ marginBottom: '8px' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <ClockCircleOutlined style={{ marginRight: '4px' }} />
            预计剩余时间: {formatEstimatedTime(estimatedTime)}
          </Text>
        </div>
      )}

      {/* 错误信息 */}
      {error && status === ProcessStatus.ERROR && (
        <Alert
          message="处理失败"
          description={error}
          type="error"
          size="small"
          showIcon
          style={{ marginBottom: '8px' }}
          action={
            onRetry && (
              <Text 
                style={{ cursor: 'pointer', color: '#1890ff' }}
                onClick={onRetry}
              >
                重试
              </Text>
            )
          }
        />
      )}

      {/* 状态描述 */}
      <Text type="secondary" style={{ fontSize: '12px' }}>
        {statusConfig.description}
      </Text>

      {/* 完成状态的额外信息 */}
      {status === ProcessStatus.COMPLETED && (
        <div style={{ marginTop: '8px', padding: '8px', background: '#f6ffed', borderRadius: '4px' }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Text style={{ fontSize: '12px', color: '#52c41a' }}>
              <CheckCircleOutlined style={{ marginRight: '4px' }} />
              处理成功完成
            </Text>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              您可以查看左侧的异常检测和数据提取结果，或与AI助手交流获取更多信息。
            </Text>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default ProcessStatusComponent;
