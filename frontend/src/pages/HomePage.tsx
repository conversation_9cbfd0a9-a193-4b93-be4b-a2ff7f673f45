/**
 * 首页组件
 * 系统主页面
 */

import React, { useEffect, useState } from 'react';
import { Card, Statistic, Row, Col, Alert, Spin } from 'antd';
import { 
  FileTextOutlined, 
  BugOutlined, 
  CheckCircleOutlined,
  ClockCircleOutlined 
} from '@ant-design/icons';
import { apiService } from '../services/api';

const HomePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [systemInfo, setSystemInfo] = useState<any>(null);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSystemData();
  }, []);

  const loadSystemData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 并行获取系统信息和健康状态
      const [infoResponse, healthResponse] = await Promise.all([
        apiService.getSystemInfo(),
        apiService.healthCheck()
      ]);

      setSystemInfo(infoResponse);
      setHealthStatus(healthResponse);
    } catch (err) {
      console.error('加载系统数据失败:', err);
      setError(err instanceof Error ? err.message : '加载系统数据失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '200px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="系统连接失败"
        description={error}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  return (
    <div style={{ padding: '20px' }}>
      {/* 系统状态卡片 */}
      <Row gutter={16} style={{ marginBottom: '20px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={healthStatus?.status === 'healthy' ? '正常' : '异常'}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: healthStatus?.status === 'healthy' ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="支持格式"
              value="Word文档"
              prefix={<FileTextOutlined />}
              suffix="(.doc/.docx)"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="提取数据点"
              value={107}
              prefix={<BugOutlined />}
              suffix="项"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行时间"
              value={healthStatus?.uptime ? Math.floor(healthStatus.uptime / 60) : 0}
              prefix={<ClockCircleOutlined />}
              suffix="分钟"
            />
          </Card>
        </Col>
      </Row>

      {/* 系统信息卡片 */}
      <Card title="系统信息" style={{ marginBottom: '20px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <p><strong>系统名称:</strong> {systemInfo?.name}</p>
            <p><strong>版本:</strong> {systemInfo?.version}</p>
            <p><strong>描述:</strong> {systemInfo?.description}</p>
          </Col>
          <Col span={12}>
            <p><strong>MaxKB智能体:</strong></p>
            <ul>
              <li>审查智能体: {systemInfo?.maxkb_agents?.review_agent}</li>
              <li>提取智能体: {systemInfo?.maxkb_agents?.extract_agent}</li>
            </ul>
          </Col>
        </Row>
      </Card>

      {/* 功能特性卡片 */}
      <Card title="功能特性">
        <Row gutter={16}>
          {systemInfo?.features?.map((feature: string, index: number) => (
            <Col span={12} key={index} style={{ marginBottom: '8px' }}>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
              {feature}
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  );
};

export default HomePage;
