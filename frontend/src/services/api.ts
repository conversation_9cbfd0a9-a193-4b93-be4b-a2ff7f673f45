/**
 * API服务基础类
 * 封装与后端API的通信逻辑
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  BaseResponse,
  ErrorResponse,
  FileUploadResponse,
  ProcessStatusResponse,
  ProcessResultResponse,
  SystemConfig
} from '../types';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log('API请求:', config.method?.toUpperCase(), config.url);
        return config;
      },
      (error) => {
        console.error('请求错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log('API响应:', response.status, response.config.url);
        return response;
      },
      (error) => {
        console.error('响应错误:', error);
        if (error.response) {
          // 服务器返回错误状态码
          const errorData: ErrorResponse = error.response.data;
          throw new Error(errorData.message || '服务器错误');
        } else if (error.request) {
          // 请求发送失败
          throw new Error('网络连接失败，请检查网络设置');
        } else {
          // 其他错误
          throw new Error(error.message || '未知错误');
        }
      }
    );
  }

  // 健康检查
  async healthCheck(): Promise<any> {
    const response = await this.client.get('/api/health');
    return response.data;
  }

  // 获取系统信息
  async getSystemInfo(): Promise<any> {
    const response = await this.client.get('/api/info');
    return response.data;
  }

  // 获取系统配置
  async getSystemConfig(): Promise<SystemConfig> {
    const response = await this.client.get('/api/config');
    return response.data;
  }

  // 创建会话
  async createSession(): Promise<any> {
    const response = await this.client.post('/api/session');
    return response.data;
  }

  // 检查会话是否存在
  async checkSessionExists(sessionId: string): Promise<any> {
    const response = await this.client.get(`/api/sessions/${sessionId}/exists`);
    return response.data;
  }

  // 文件上传
  async uploadFile(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log('上传进度:', progress + '%');
        }
      },
    });

    return response.data;
  }

  // 获取上传配置
  async getUploadConfig(): Promise<any> {
    const response = await this.client.get('/api/upload/config');
    return response.data;
  }

  // 删除上传文件
  async deleteUploadedFile(sessionId: string): Promise<any> {
    const response = await this.client.delete(`/api/upload/${sessionId}`);
    return response.data;
  }

  // 触发文档处理
  async processDocument(sessionId: string): Promise<BaseResponse> {
    const response = await this.client.post(`/api/parse/${sessionId}`);
    return response.data;
  }

  // 查询处理状态
  async getProcessStatus(sessionId: string): Promise<ProcessStatusResponse> {
    const response = await this.client.get(`/api/status/${sessionId}`);
    return response.data;
  }

  // 获取处理结果
  async getProcessResults(sessionId: string): Promise<ProcessResultResponse> {
    const response = await this.client.get(`/api/results/${sessionId}`);
    return response.data;
  }

  // 清理会话文件
  async cleanupSession(sessionId: string): Promise<BaseResponse> {
    const response = await this.client.delete(`/api/cleanup/${sessionId}`);
    return response.data;
  }

  // 获取统计信息
  async getStats(): Promise<any> {
    const response = await this.client.get('/api/stats');
    return response.data;
  }
}

// 创建全局API服务实例
export const apiService = new ApiService();
export default apiService;
