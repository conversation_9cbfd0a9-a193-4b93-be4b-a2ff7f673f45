/**
 * 聊天服务
 * 处理AI助手的消息发送和接收
 */

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  metadata?: {
    sessionId?: string;
    documentId?: string;
    messageType?: 'question' | 'analysis' | 'suggestion';
  };
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

class ChatService {
  private sessions: Map<string, ChatSession> = new Map();
  private currentSessionId: string | null = null;
  private storageKey = 'chat_sessions';

  constructor() {
    this.loadFromStorage();
  }

  /**
   * 创建新的聊天会话
   */
  createSession(sessionId?: string): string {
    const id = sessionId || `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: ChatSession = {
      id,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.sessions.set(id, session);
    this.currentSessionId = id;
    this.saveToStorage();
    
    return id;
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): ChatSession | null {
    if (!this.currentSessionId) return null;
    return this.sessions.get(this.currentSessionId) || null;
  }

  /**
   * 设置当前会话
   */
  setCurrentSession(sessionId: string): boolean {
    if (this.sessions.has(sessionId)) {
      this.currentSessionId = sessionId;
      return true;
    }
    return false;
  }

  /**
   * 添加消息到当前会话
   */
  addMessage(message: Omit<ChatMessage, 'id' | 'timestamp'>): ChatMessage {
    const session = this.getCurrentSession();
    if (!session) {
      throw new Error('No active chat session');
    }

    const newMessage: ChatMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    session.messages.push(newMessage);
    session.updatedAt = new Date();
    this.saveToStorage();

    return newMessage;
  }

  /**
   * 更新消息状态
   */
  updateMessageStatus(messageId: string, status: ChatMessage['status']): boolean {
    const session = this.getCurrentSession();
    if (!session) return false;

    const message = session.messages.find(msg => msg.id === messageId);
    if (message) {
      message.status = status;
      session.updatedAt = new Date();
      this.saveToStorage();
      return true;
    }
    return false;
  }

  /**
   * 发送消息给AI助手
   */
  async sendMessage(content: string, metadata?: ChatMessage['metadata']): Promise<ChatMessage> {
    // 添加用户消息
    const userMessage = this.addMessage({
      type: 'user',
      content,
      status: 'sent',
      metadata
    });

    // 添加AI回复占位符
    const aiMessage = this.addMessage({
      type: 'ai',
      content: '',
      status: 'sending'
    });

    try {
      // 模拟AI回复（实际项目中这里会调用真实的AI API）
      const aiResponse = await this.simulateAIResponse(content, metadata);
      
      // 更新AI消息
      const session = this.getCurrentSession();
      if (session) {
        const message = session.messages.find(msg => msg.id === aiMessage.id);
        if (message) {
          message.content = aiResponse;
          message.status = 'sent';
          session.updatedAt = new Date();
          this.saveToStorage();
        }
      }

      return aiMessage;
    } catch (error) {
      this.updateMessageStatus(aiMessage.id, 'error');
      throw error;
    }
  }

  /**
   * 模拟AI回复
   */
  private async simulateAIResponse(userMessage: string, metadata?: ChatMessage['metadata']): Promise<string> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lowerMessage = userMessage.toLowerCase();

    // 根据用户消息内容生成相应回复
    if (lowerMessage.includes('异常') || lowerMessage.includes('问题')) {
      return `我已经分析了文档中的异常情况。根据智能审查结果，发现了以下主要问题：

1. **缺少项目预算信息**：招标文件中未明确说明项目预算金额，建议补充详细的项目预算说明。

2. **日期格式不统一**：文档中日期格式存在不一致，建议统一使用YYYY-MM-DD格式。

您可以点击左侧异常列表中的具体项目来查看详细位置和建议修改方案。`;
    }

    if (lowerMessage.includes('提取') || lowerMessage.includes('数据')) {
      return `数据提取已完成！我成功从招标文件中提取了以下关键信息：

**基本信息**：
- 项目名称：某某工程建设项目
- 招标编号：ZB2025-001  
- 招标人：某某建设集团有限公司

**投标要求**：
- 注册资本要求：不少于1000万元人民币

**商务条款**：
- 付款方式：按工程进度分期付款

当前数据提取完成率为100%，所有必填项都已成功提取。您可以在左侧面板查看详细的提取结果。`;
    }

    if (lowerMessage.includes('建议') || lowerMessage.includes('优化')) {
      return `基于文档分析，我为您提供以下优化建议：

**文档完善建议**：
1. 补充详细的项目预算信息和资金来源说明
2. 统一文档中的日期格式，建议使用标准格式
3. 增加更详细的技术规格要求

**投标流程建议**：
1. 明确投标文件的具体要求和评分标准
2. 提供更清晰的时间节点安排
3. 补充质量保证和售后服务条款

这些改进将有助于提高招标文件的专业性和完整性。`;
    }

    if (lowerMessage.includes('帮助') || lowerMessage.includes('功能')) {
      return `我是您的AI助手，可以帮助您：

🔍 **文档分析**：
- 智能检测文档中的异常和问题
- 提取关键数据和信息点
- 提供文档优化建议

📋 **交互功能**：
- 点击左侧列表项可定位到文档位置
- 使用搜索功能快速查找内容
- 缩放和导出文档

💬 **智能问答**：
- 询问文档内容和分析结果
- 获取专业的改进建议
- 了解招标流程和要求

您可以直接提问，我会根据当前文档内容为您提供专业解答。`;
    }

    // 默认回复
    return `感谢您的提问。我已经收到您的消息："${userMessage}"

作为您的AI助手，我可以帮助您分析招标文件、解答相关问题、提供优化建议。

您可以询问：
- 文档中的异常情况
- 数据提取结果
- 文档优化建议
- 系统功能使用方法

请告诉我您需要了解什么，我会为您提供详细的解答。`;
  }

  /**
   * 获取快捷问题
   */
  getQuickQuestions(): string[] {
    return [
      '文档中有哪些异常需要注意？',
      '数据提取的结果如何？',
      '有什么优化建议吗？',
      '如何使用系统功能？',
      '招标文件是否完整？',
      '投标要求有哪些？'
    ];
  }

  /**
   * 清空当前会话
   */
  clearCurrentSession(): void {
    const session = this.getCurrentSession();
    if (session) {
      session.messages = [];
      session.updatedAt = new Date();
      this.saveToStorage();
    }
  }

  /**
   * 删除会话
   */
  deleteSession(sessionId: string): boolean {
    if (this.sessions.delete(sessionId)) {
      if (this.currentSessionId === sessionId) {
        this.currentSessionId = null;
      }
      this.saveToStorage();
      return true;
    }
    return false;
  }

  /**
   * 导出聊天记录
   */
  exportChatHistory(sessionId?: string): string {
    const session = sessionId ? 
      this.sessions.get(sessionId) : 
      this.getCurrentSession();
    
    if (!session) return '';

    const lines = [`聊天记录导出 - ${session.id}`, `导出时间: ${new Date().toLocaleString()}`, ''];
    
    session.messages.forEach(msg => {
      const time = msg.timestamp.toLocaleTimeString();
      const sender = msg.type === 'user' ? '用户' : msg.type === 'ai' ? 'AI助手' : '系统';
      lines.push(`[${time}] ${sender}: ${msg.content}`);
      lines.push('');
    });

    return lines.join('\n');
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage(): void {
    try {
      const data = {
        sessions: Array.from(this.sessions.entries()),
        currentSessionId: this.currentSessionId
      };
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save chat sessions:', error);
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (data) {
        const parsed = JSON.parse(data);
        this.sessions = new Map(parsed.sessions || []);
        this.currentSessionId = parsed.currentSessionId || null;
        
        // 转换日期字符串为Date对象
        this.sessions.forEach(session => {
          session.createdAt = new Date(session.createdAt);
          session.updatedAt = new Date(session.updatedAt);
          session.messages.forEach(msg => {
            msg.timestamp = new Date(msg.timestamp);
          });
        });
      }
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
    }
  }
}

export const chatService = new ChatService();
