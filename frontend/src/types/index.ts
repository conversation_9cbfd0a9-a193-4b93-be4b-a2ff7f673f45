/**
 * TypeScript类型定义
 * 定义前端应用中使用的数据类型
 */

// 基础响应类型
export interface BaseResponse {
  success: boolean;
  message: string;
  timestamp: string;
}

export interface ErrorResponse {
  error: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

// 文件上传相关类型
export interface FileUploadResponse extends BaseResponse {
  session_id: string;
  filename: string;
  file_size: number;
  upload_time: string;
}

// 处理状态枚举
export enum ProcessStatus {
  UPLOADED = 'uploaded',
  PARSING = 'parsing',
  REVIEWING = 'reviewing',
  EXTRACTING = 'extracting',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 处理状态响应
export interface ProcessStatusResponse extends BaseResponse {
  session_id: string;
  status: ProcessStatus;
  progress: number;
  current_step: string;
  estimated_time?: number;
}

// 异常检测相关类型
export enum AnomalyLevel {
  CRITICAL = 'critical',
  WARNING = 'warning',
  INFO = 'info'
}

export interface AnomalyItem {
  id: string;
  type: string;
  level: AnomalyLevel;
  title: string;
  description: string;
  location: string;
  page_number?: number;
  paragraph_number?: number;
  suggestion?: string;
  original_text?: string;
}

// 数据提取相关类型
export interface ExtractionItem {
  id: string;
  name: string;
  category: string;
  content?: string;
  location: string;
  page_number?: number;
  paragraph_number?: number;
  confidence: number;
  original_text?: string;
}

// 处理结果响应
export interface ProcessResultResponse extends BaseResponse {
  session_id: string;
  document_html: string;
  anomalies: AnomalyItem[];
  extractions: ExtractionItem[];
  total_anomalies: number;
  total_extractions: number;
  processing_time: number;
}

// 文档信息
export interface DocumentInfo {
  filename: string;
  file_size: number;
  page_count?: number;
  word_count?: number;
  upload_time: string;
  parse_time?: string;
}

// 系统配置
export interface SystemConfig {
  max_file_size: number;
  allowed_extensions: string[];
  temp_dir: string;
  session_timeout: number;
  supported_languages: string[];
  maxkb_config: {
    base_url: string;
    review_agent_id: string;
    extract_agent_id: string;
    timeout: number;
  };
  extraction_points: number;
  timestamp: string;
}

// 聊天消息类型
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: string;
}

// 应用状态
export interface AppState {
  currentSessionId?: string;
  documentInfo?: DocumentInfo;
  processStatus?: ProcessStatus;
  anomalies: AnomalyItem[];
  extractions: ExtractionItem[];
  documentHtml?: string;
  chatMessages: ChatMessage[];
  loading: boolean;
  error?: string;
}
