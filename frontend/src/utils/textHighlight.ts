/**
 * 文本高亮工具函数
 * 用于在HTML文档中实现文本定位和高亮功能
 */

export interface HighlightPosition {
  id: string;
  text: string;
  startOffset: number;
  endOffset: number;
  className?: string;
  color?: string;
}

export interface SearchResult {
  text: string;
  positions: HighlightPosition[];
  totalMatches: number;
}

export class TextHighlighter {
  private container: HTMLElement;
  private highlightClass = 'text-highlight';
  private searchClass = 'search-highlight';
  private currentHighlights: HighlightPosition[] = [];

  constructor(container: HTMLElement) {
    this.container = container;
    this.addHighlightStyles();
  }

  /**
   * 添加高亮样式到页面
   */
  private addHighlightStyles(): void {
    const styleId = 'text-highlighter-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      .${this.highlightClass} {
        background-color: rgba(255, 255, 0, 0.4);
        border: 2px solid #ff4444;
        border-radius: 3px;
        padding: 1px 2px;
        margin: 0 1px;
        transition: all 0.3s ease;
      }
      
      .${this.searchClass} {
        background-color: rgba(0, 123, 255, 0.3);
        border: 1px solid #007bff;
        border-radius: 2px;
        padding: 1px;
      }
      
      .${this.highlightClass}:hover {
        background-color: rgba(255, 255, 0, 0.6);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      .highlight-anomaly {
        background-color: rgba(255, 0, 0, 0.2);
        border: 2px solid #ff4444;
      }
      
      .highlight-extraction {
        background-color: rgba(0, 255, 0, 0.2);
        border: 2px solid #28a745;
      }

      .selected-highlight {
        background-color: rgba(255, 215, 0, 0.8) !important;
        border: 3px solid #ffa500 !important;
        box-shadow: 0 0 10px rgba(255, 165, 0, 0.6);
        animation: pulse-highlight 2s ease-in-out;
      }

      @keyframes pulse-highlight {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 高亮指定文本
   */
  highlightText(text: string, className?: string): HighlightPosition[] {
    if (!text || text.length < 2) return [];

    const positions = this.findTextPositions(text);
    positions.forEach(position => {
      position.className = className || this.highlightClass;
      this.applyHighlight(position);
    });

    this.currentHighlights.push(...positions);
    return positions;
  }

  /**
   * 搜索文本并高亮
   */
  searchAndHighlight(searchText: string): SearchResult {
    this.clearSearchHighlights();
    
    if (!searchText || searchText.length < 2) {
      return { text: searchText, positions: [], totalMatches: 0 };
    }

    const positions = this.findTextPositions(searchText);
    positions.forEach(position => {
      position.className = this.searchClass;
      this.applyHighlight(position);
    });

    return {
      text: searchText,
      positions,
      totalMatches: positions.length
    };
  }

  /**
   * 查找文本在DOM中的位置
   */
  private findTextPositions(searchText: string): HighlightPosition[] {
    const positions: HighlightPosition[] = [];
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let textNode: Text | null;
    let globalOffset = 0;

    while (textNode = walker.nextNode() as Text) {
      const text = textNode.textContent || '';
      const lowerText = text.toLowerCase();
      const lowerSearch = searchText.toLowerCase();
      
      let startIndex = 0;
      let foundIndex: number;

      while ((foundIndex = lowerText.indexOf(lowerSearch, startIndex)) !== -1) {
        positions.push({
          id: `highlight_${Date.now()}_${positions.length}`,
          text: text.substring(foundIndex, foundIndex + searchText.length),
          startOffset: globalOffset + foundIndex,
          endOffset: globalOffset + foundIndex + searchText.length,
          className: this.highlightClass
        });

        startIndex = foundIndex + 1;
      }

      globalOffset += text.length;
    }

    return positions;
  }

  /**
   * 应用高亮效果
   */
  private applyHighlight(position: HighlightPosition): void {
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let textNode: Text | null;
    let currentOffset = 0;

    while (textNode = walker.nextNode() as Text) {
      const text = textNode.textContent || '';
      const nodeStart = currentOffset;
      const nodeEnd = currentOffset + text.length;

      // 检查高亮位置是否在当前文本节点中
      if (position.startOffset >= nodeStart && position.startOffset < nodeEnd) {
        const relativeStart = position.startOffset - nodeStart;
        const relativeEnd = Math.min(position.endOffset - nodeStart, text.length);

        // 分割文本节点并插入高亮元素
        this.wrapTextWithHighlight(textNode, relativeStart, relativeEnd, position);
        break;
      }

      currentOffset += text.length;
    }
  }

  /**
   * 用高亮元素包装文本
   */
  private wrapTextWithHighlight(
    textNode: Text, 
    start: number, 
    end: number, 
    position: HighlightPosition
  ): void {
    const text = textNode.textContent || '';
    const parent = textNode.parentNode;
    
    if (!parent) return;

    // 创建高亮元素
    const highlightSpan = document.createElement('span');
    highlightSpan.className = position.className || this.highlightClass;
    highlightSpan.setAttribute('data-highlight-id', position.id);
    highlightSpan.textContent = text.substring(start, end);

    // 分割原文本节点
    const beforeText = text.substring(0, start);
    const afterText = text.substring(end);

    // 替换原节点
    if (beforeText) {
      const beforeNode = document.createTextNode(beforeText);
      parent.insertBefore(beforeNode, textNode);
    }

    parent.insertBefore(highlightSpan, textNode);

    if (afterText) {
      const afterNode = document.createTextNode(afterText);
      parent.insertBefore(afterNode, textNode);
    }

    parent.removeChild(textNode);
  }

  /**
   * 滚动到指定高亮位置
   */
  scrollToHighlight(highlightId: string): void {
    const element = this.container.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });

      // 临时添加闪烁效果
      element.classList.add('highlight-flash');
      setTimeout(() => {
        element.classList.remove('highlight-flash');
      }, 2000);
    }
  }

  /**
   * 清除所有高亮
   */
  clearAllHighlights(): void {
    const highlights = this.container.querySelectorAll(`[data-highlight-id]`);
    highlights.forEach(highlight => {
      const parent = highlight.parentNode;
      if (parent) {
        parent.replaceChild(
          document.createTextNode(highlight.textContent || ''),
          highlight
        );
        parent.normalize(); // 合并相邻的文本节点
      }
    });

    this.currentHighlights = [];
  }

  /**
   * 清除搜索高亮
   */
  clearSearchHighlights(): void {
    const searchHighlights = this.container.querySelectorAll(`.${this.searchClass}`);
    searchHighlights.forEach(highlight => {
      const parent = highlight.parentNode;
      if (parent) {
        parent.replaceChild(
          document.createTextNode(highlight.textContent || ''),
          highlight
        );
        parent.normalize();
      }
    });
  }

  /**
   * 获取当前高亮数量
   */
  getHighlightCount(): number {
    return this.currentHighlights.length;
  }

  /**
   * 高亮异常位置
   */
  highlightAnomalies(anomalies: Array<{ id: string; original_text?: string }>): void {
    anomalies.forEach(anomaly => {
      if (anomaly.original_text) {
        this.highlightText(anomaly.original_text, 'highlight-anomaly');
      }
    });
  }

  /**
   * 高亮提取位置
   */
  highlightExtractions(extractions: Array<{ id: string; original_text?: string }>): void {
    extractions.forEach(extraction => {
      if (extraction.original_text) {
        this.highlightText(extraction.original_text, 'highlight-extraction');
      }
    });
  }
}
