# MaxKB API集成指南

## 🎯 集成概述
本文档说明如何将真实的MaxKB智能体API集成到招标文件智能审查与提取系统中。

## 📋 当前状态
- ✅ **系统架构完成**: 已实现完整的API调用框架
- ✅ **测试模式可用**: 当前使用模拟数据进行演示
- 🔄 **待集成真实API**: 等待MaxKB API配置信息

## 🔧 配置步骤

### 1. 更新后端配置
**文件位置**: `backend/app/core/config.py`

**需要更新的配置项**:
```python
class Settings:
    # MaxKB API配置
    maxkb_base_url: str = "您提供的MaxKB API基础URL"
    maxkb_api_key: str = "您提供的API密钥"
    maxkb_review_agent_id: str = "审查智能体ID"
    maxkb_extract_agent_id: str = "提取智能体ID"
    maxkb_timeout: int = 300  # 超时时间(秒)
    maxkb_test_mode: bool = False  # 关闭测试模式
```

### 2. API调用实现
**文件位置**: `backend/app/services/maxkb_client.py`

**当前实现的API调用方法**:
```python
class MaxKBClient:
    async def call_review_agent(self, session_id: str, document_text: str) -> Dict[str, Any]
    async def call_extract_agent(self, session_id: str, document_text: str) -> Dict[str, Any]
    async def process_document_with_agents(self, session_id: str, document_text: str) -> Dict[str, Any]
```

## 📊 API数据格式

### 审查智能体预期返回格式
```json
{
  "anomalies": [
    {
      "id": "anomaly_001",
      "type": "missing_info",
      "level": "warning",
      "title": "异常标题",
      "description": "异常描述",
      "location": "文档位置",
      "suggestion": "修改建议",
      "original_text": "原文内容",
      "confidence": 0.85
    }
  ],
  "summary": "审查总结",
  "total_anomalies": 1
}
```

### 提取智能体预期返回格式
```json
{
  "extractions": [
    {
      "id": "001",
      "name": "数据点名称",
      "category": "basic_info",
      "content": "提取的内容",
      "location": "文档位置",
      "confidence": 0.95,
      "original_text": "原文内容",
      "is_required": true,
      "data_type": "text"
    }
  ],
  "summary": "提取总结",
  "total_extractions": 1
}
```

## 🔄 集成流程

### 步骤1: 配置API信息
1. 获取MaxKB API的基础URL
2. 获取API访问密钥
3. 获取审查智能体ID
4. 获取提取智能体ID
5. 更新`backend/app/core/config.py`配置

### 步骤2: 测试API连接
```bash
# 启动后端服务
cd backend
python run.py

# 测试API连接
curl -X POST "http://localhost:8000/api/process/test-maxkb" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

### 步骤3: 关闭测试模式
在`backend/app/services/maxkb_client.py`中设置:
```python
self.test_mode = False  # 关闭测试模式
```

### 步骤4: 验证集成
1. 上传真实的招标文件
2. 启动文档处理
3. 验证返回的异常检测结果
4. 验证返回的数据提取结果
5. 检查前端显示是否正常

## 🛠️ 调试指南

### 日志查看
**后端日志位置**: 控制台输出
**关键日志标识**:
- `调用审查智能体，会话ID: {session_id}`
- `调用提取智能体，会话ID: {session_id}`
- `智能体调用异常: {error}`

### 常见问题排查

#### 1. API连接失败
**可能原因**:
- API URL配置错误
- API密钥无效
- 网络连接问题

**排查方法**:
```python
# 在maxkb_client.py中添加调试日志
logger.info(f"API URL: {self.base_url}")
logger.info(f"Request headers: {headers}")
logger.info(f"Request data: {request_data}")
```

#### 2. 返回数据格式不匹配
**可能原因**:
- MaxKB返回格式与预期不符
- 数据解析错误

**解决方法**:
1. 查看实际返回的JSON格式
2. 调整`_parse_review_result`和`_parse_extract_result`方法
3. 更新前端数据类型定义

#### 3. 智能体响应超时
**可能原因**:
- 文档内容过长
- 智能体处理时间过长

**解决方法**:
1. 增加超时时间配置
2. 分段处理大文档
3. 添加重试机制

## 📝 数据映射说明

### 异常类型映射
```python
ANOMALY_TYPE_MAP = {
    'missing_info': '信息缺失',
    'inconsistent_data': '数据不一致', 
    'format_error': '格式错误',
    'logic_error': '逻辑错误',
    'compliance_issue': '合规问题',
    'other': '其他'
}
```

### 提取分类映射
```python
EXTRACTION_CATEGORY_MAP = {
    'basic_info': '基本信息',
    'bidder_requirements': '投标人要求',
    'technical_specs': '技术规格',
    'commercial_terms': '商务条款',
    'evaluation_criteria': '评标标准',
    'contract_terms': '合同条款',
    'other': '其他'
}
```

## 🔒 安全注意事项

### API密钥安全
- 不要在代码中硬编码API密钥
- 使用环境变量存储敏感信息
- 定期轮换API密钥

### 数据安全
- 确保文档内容不会被永久存储在MaxKB服务中
- 验证返回数据的完整性
- 实施适当的错误处理避免信息泄露

## 🧪 测试用例

### 测试文档准备
建议准备以下类型的测试文档:
1. **标准招标文件**: 包含完整信息的正常文档
2. **缺失信息文档**: 故意缺少某些关键信息
3. **格式错误文档**: 包含格式不规范的内容
4. **大型文档**: 测试性能和超时处理

### 预期测试结果
- **异常检测**: 应能识别出文档中的问题
- **数据提取**: 应能提取出关键的107个数据点
- **定位准确性**: 点击列表项应能准确定位到文档位置
- **性能表现**: 处理时间应在合理范围内

## 📞 技术支持

### 集成支持
如果在集成过程中遇到问题，请提供以下信息:
1. 错误日志的完整输出
2. API调用的请求和响应数据
3. 使用的测试文档类型
4. 系统环境信息

### 联系方式
- **开发团队**: 通过项目沟通渠道
- **技术文档**: 参考本项目的其他文档
- **问题反馈**: 通过项目管理工具提交

---

**文档版本**: v1.0  
**最后更新**: 2025年1月3日  
**状态**: 待API配置信息
