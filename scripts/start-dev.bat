@echo off
echo 🚀 启动招标文件智能审查与提取系统开发环境

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 启动后端
echo 📦 启动后端服务...
cd backend

if not exist venv (
    echo 创建Python虚拟环境...
    python -m venv venv
)

call venv\Scripts\activate
pip install -r requirements.txt

echo 🔧 启动FastAPI服务器...
start "Backend Server" cmd /k "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

cd ..

REM 启动前端
echo 🎨 启动前端服务...
cd frontend
call npm install
start "Frontend Server" cmd /k "npm run dev"

cd ..

echo ✅ 服务启动完成!
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
pause
